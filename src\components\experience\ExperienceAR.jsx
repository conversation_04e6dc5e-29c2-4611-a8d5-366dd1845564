import { XR } from '@react-three/xr'
import React, { useEffect } from 'react'
import ExperienceModels from './ExperienceModels'

export default function ExperienceAR({data,store}) {
  const startAR = (params) => {
    store.enterAR()
  }

  const endAR = (params) => {
    store.destroy()
  }
  
  useEffect(() => {
    startAR()
    return () => {
      endAR()
    }
  }, [])
  
  return (
    <XR store={store}>
      <group>
        <ExperienceModels data={data}/>
      </group>
    </XR>
  )
}
