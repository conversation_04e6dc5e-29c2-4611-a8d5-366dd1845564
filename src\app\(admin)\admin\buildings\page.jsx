// src/app/admin/buildings/page.jsx
// Building management dashboard page

"use client";

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';
import Link from 'next/link';
import {
  HiPlus,
  HiSearch,
  HiFilter,
  HiEye,
  HiPencil,
  HiTrash,
  HiChevronLeft,
  HiChevronRight,
  HiHome,
  HiOfficeBuilding
} from 'react-icons/hi';

export default function BuildingsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [buildings, setBuildings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [searchTerm, setSearchTerm] = useState('');
  const [buildingTypeFilter, setBuildingTypeFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [pagination, setPagination] = useState({});

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'loading') return;
    if (!session) {
      router.push('/auth/signin');
      return;
    }
  }, [session, status, router]);

  // Fetch buildings
  const fetchBuildings = async (page = 1, search = '', buildingType = '') => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: page.toString(),
        limit: '10',
        ...(search && { search }),
        ...(buildingType && { buildingType })
      });

      const response = await fetch(`/api/buildings?${params}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch buildings');
      }

      const data = await response.json();
      setBuildings(data.buildings);
      setPagination(data.pagination);
      setError('');
    } catch (err) {
      setError(err.message);
      setBuildings([]);
    } finally {
      setLoading(false);
    }
  };

  // Initial load
  useEffect(() => {
    // if (session) {
    //   fetchBuildings();
    // }
    fetchBuildings();
  }, [session]);

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchBuildings(1, searchTerm, buildingTypeFilter);
  };

  // Handle filter change
  const handleFilterChange = (buildingType) => {
    setBuildingTypeFilter(buildingType);
    setCurrentPage(1);
    fetchBuildings(1, searchTerm, buildingType);
  };

  // Handle pagination
  const handlePageChange = (page) => {
    setCurrentPage(page);
    fetchBuildings(page, searchTerm, buildingTypeFilter);
  };

  // Handle delete
  const handleDelete = async (buildingId, projectTitle) => {
    if (!confirm(`Are you sure you want to delete "${projectTitle}"? This action cannot be undone.`)) {
      return;
    }

    try {
      const response = await fetch(`/api/buildings/${buildingId}`, {
        method: 'DELETE'
      });

      if (!response.ok) {
        throw new Error('Failed to delete building');
      }

      // Refresh the list
      fetchBuildings(currentPage, searchTerm, buildingTypeFilter);
      alert('Building deleted successfully');
    } catch (err) {
      alert('Error deleting building: ' + err.message);
    }
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!session) {
    return null;
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white border-b border-neutral-200 sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center space-x-4">
              <Link
                href="/admin"
                className="flex items-center text-neutral-600 hover:text-neutral-900 transition-colors"
              >
                <HiChevronLeft className="w-5 h-5 mr-2" />
                <span className="font-light">Back to Admin</span>
              </Link>
              <div className="h-6 w-px bg-neutral-300"></div>
              <Link href="/" className="text-2xl font-thin tracking-widest text-neutral-900 hover:text-neutral-700 transition-colors">
                luyari.
              </Link>
            </div>
            <div className="text-sm text-neutral-600 font-light">
              Building Management
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Page Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="text-center mb-12"
        >
          <div className="flex items-center justify-center mb-4">
            <HiOfficeBuilding className="w-8 h-8 text-neutral-600 mr-3" />
            <h1 className="chaumet-heading text-4xl text-neutral-900">Building Management</h1>
          </div>
          <p className="text-neutral-600 font-light max-w-2xl mx-auto">
            Manage your building projects, files, and architectural designs with comprehensive tools.
          </p>
          <div className="chaumet-divider mt-8 mx-auto" style={{ width: '4rem' }} />
        </motion.div>

        {/* Action Bar */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.2 }}
          className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8"
        >
          <div className="flex flex-col sm:flex-row gap-4 flex-1">
            {/* Search */}
            <div className="relative flex-1 max-w-md">
              <HiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
              <input
                type="text"
                placeholder="Search buildings..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="chaumet-input pl-10"
              />
            </div>

            {/* Filter */}
            <div className="relative">
              <HiFilter className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 w-5 h-5" />
              <select
                value={buildingTypeFilter}
                onChange={(e) => setBuildingTypeFilter(e.target.value)}
                className="chaumet-select pl-10 min-w-48"
              >
                <option value="">All Building Types</option>
                <option value="multi-storey">Multi-Storey</option>
                <option value="single-storey">Single-Storey</option>
                <option value="multi-residence">Multi-Residence</option>
              </select>
            </div>
          </div>

          <Link
            href="/admin/buildings/create"
            className="chaumet-button border-neutral-900 text-neutral-900 group"
          >
            <span className="relative z-10 group-hover:text-white transition-colors duration-500 flex items-center">
              <HiPlus className="w-4 h-4 mr-2" />
              Create Building
            </span>
          </Link>
        </motion.div>

        {/* Error Message */}
        {error && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6"
          >
            <p className="text-red-800 font-light">{error}</p>
          </motion.div>
        )}

        {/* Buildings List */}
        {loading ? (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="bg-white rounded-lg border border-neutral-200 p-12 text-center"
          >
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-neutral-900 mx-auto"></div>
            <p className="text-neutral-600 mt-4 font-light">Loading buildings...</p>
          </motion.div>
        ) : buildings.length === 0 ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-lg border border-neutral-200 p-12 text-center"
          >
            <HiOfficeBuilding className="w-16 h-16 text-neutral-300 mx-auto mb-4" />
            <h3 className="chaumet-heading text-xl text-neutral-900 mb-2">No Buildings Found</h3>
            <p className="text-neutral-600 font-light mb-6">
              {searchTerm || buildingTypeFilter
                ? "No buildings match your search criteria."
                : "Start by creating your first building project."
              }
            </p>
            <Link
              href="/admin/buildings/create"
              className="chaumet-button border-neutral-900 text-neutral-900 group"
            >
              <span className="relative z-10 group-hover:text-white transition-colors duration-500 flex items-center">
                <HiPlus className="w-4 h-4 mr-2" />
                Create Your First Building
              </span>
            </Link>
          </motion.div>
        ) : (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="bg-white rounded-lg border border-neutral-200 overflow-hidden"
          >
            <div className="overflow-x-auto">
              <table className="min-w-full">
                <thead className="bg-neutral-50 border-b border-neutral-200">
                  <tr>
                    <th className="px-6 py-4 text-left text-xs font-light text-neutral-600 uppercase tracking-widest">
                      Project
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-light text-neutral-600 uppercase tracking-widest">
                      Type
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-light text-neutral-600 uppercase tracking-widest">
                      Price
                    </th>
                    <th className="px-6 py-4 text-left text-xs font-light text-neutral-600 uppercase tracking-widest">
                      Created
                    </th>
                    <th className="px-6 py-4 text-right text-xs font-light text-neutral-600 uppercase tracking-widest">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white">
                  {buildings.map((building, index) => (
                    <motion.tr
                      key={building._id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: index * 0.1 }}
                      className="border-b border-neutral-100 hover:bg-neutral-50 transition-colors duration-300"
                    >
                      <td className="px-6 py-6">
                        <div>
                          <div className="text-sm font-light text-neutral-900 mb-1">
                            {building.projectTitle}
                          </div>
                          <div className="text-xs text-neutral-500 font-light">
                            {building.buildingTitle}
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-6">
                        <span className="inline-flex px-3 py-1 text-xs font-light rounded-full bg-neutral-100 text-neutral-700 border border-neutral-200">
                          {building.buildingType.replace('-', ' ')}
                        </span>
                      </td>
                      <td className="px-6 py-6 text-sm text-neutral-900 font-light">
                        {building.price}
                      </td>
                      <td className="px-6 py-6 text-sm text-neutral-500 font-light">
                        {new Date(building.createdAt).toLocaleDateString('en-US', {
                          year: 'numeric',
                          month: 'short',
                          day: 'numeric'
                        })}
                      </td>
                      <td className="px-6 py-6">
                        <div className="flex justify-end items-center space-x-3">
                          <Link
                            href={`/admin/buildings/${building._id}`}
                            className="flex items-center text-neutral-600 hover:text-neutral-900 transition-colors"
                            title="View Building"
                          >
                            <HiEye className="w-4 h-4" />
                          </Link>
                          <Link
                            href={`/admin/buildings/${building._id}/edit`}
                            className="flex items-center text-neutral-600 hover:text-neutral-900 transition-colors"
                            title="Edit Building"
                          >
                            <HiPencil className="w-4 h-4" />
                          </Link>
                          {session?.user?.role === 'admin' && (
                            <button
                              onClick={() => handleDelete(building._id, building.projectTitle)}
                              className="flex items-center text-neutral-600 hover:text-red-600 transition-colors"
                              title="Delete Building"
                            >
                              <HiTrash className="w-4 h-4" />
                            </button>
                          )}
                        </div>
                      </td>
                    </motion.tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Pagination */}
            {pagination.totalPages > 1 && (
              <div className="bg-neutral-50 px-6 py-4 border-t border-neutral-200">
                <div className="flex items-center justify-between">
                  <div className="flex-1 flex justify-between sm:hidden">
                    <button
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={!pagination.hasPrev}
                      className="chaumet-button border-neutral-300 text-neutral-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <span className="relative z-10 flex items-center">
                        <HiChevronLeft className="w-4 h-4 mr-1" />
                        Previous
                      </span>
                    </button>
                    <button
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={!pagination.hasNext}
                      className="chaumet-button border-neutral-300 text-neutral-600 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      <span className="relative z-10 flex items-center">
                        Next
                        <HiChevronRight className="w-4 h-4 ml-1" />
                      </span>
                    </button>
                  </div>
                  <div className="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                    <div>
                      <p className="text-sm text-neutral-600 font-light">
                        Showing page <span className="font-medium">{currentPage}</span> of{' '}
                        <span className="font-medium">{pagination.totalPages}</span>
                      </p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={!pagination.hasPrev}
                        className="chaumet-button border-neutral-300 text-neutral-600 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <span className="relative z-10 flex items-center">
                          <HiChevronLeft className="w-4 h-4 mr-1" />
                          Previous
                        </span>
                      </button>
                      <button
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={!pagination.hasNext}
                        className="chaumet-button border-neutral-300 text-neutral-600 disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        <span className="relative z-10 flex items-center">
                          Next
                          <HiChevronRight className="w-4 h-4 ml-1" />
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </motion.div>
        )}
      </main>

      {/* Footer */}
      <footer className="bg-neutral-50 border-t border-neutral-200 py-8">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center">
            <p className="text-neutral-600 font-light text-sm">
              Building Management System - Luyari Architecture
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
