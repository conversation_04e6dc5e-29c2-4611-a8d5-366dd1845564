import { auth } from "@/auth";
import { NextResponse } from "next/server";
import { MongoClient } from "mongodb";

const uri = process.env.MONGODB_URI;

async function getDatabase() {
  const client = new MongoClient(uri);
  await client.connect();
  return client.db('luyarisite');
}

export async function POST(request) {
  try {
    const session = await auth();
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { email, projectId } = body;

    // Validate input
    if (!email || !projectId) {
      return NextResponse.json({ error: 'Email and project ID are required' }, { status: 400 });
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      return NextResponse.json({ error: 'Invalid email format' }, { status: 400 });
    }

    const db = await getDatabase();
    const invitesCollection = db.collection('invites');

    // Check if invite already exists
    const existingInvite = await invitesCollection.findOne({
      senderEmail: session.user.email,
      recipientEmail: email,
      projectId: projectId
    });

    if (existingInvite) {
      return NextResponse.json({ error: 'Invite already exists for this email and project' }, { status: 400 });
    }

    // Create new invite
    const newInvite = {
      id: `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      senderEmail: session.user.email,
      senderName: session.user.name || session.user.username || 'User',
      recipientEmail: email,
      projectId: projectId,
      status: 'pending',
      createdAt: new Date().toISOString(),
      sentAt: null,
      viewedAt: null
    };

    await invitesCollection.insertOne(newInvite);

    return NextResponse.json({
      id: newInvite.id,
      email: newInvite.recipientEmail,
      projectId: newInvite.projectId,
      status: newInvite.status,
      createdAt: newInvite.createdAt
    });

  } catch (error) {
    console.error('Error creating invite:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function GET() {
  try {
    const session = await auth();
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const db = await getDatabase();
    const invitesCollection = db.collection('invites');

    // Get all invites sent by this user
    const invites = await invitesCollection.find({
      senderEmail: session.user.email
    }).sort({ createdAt: -1 }).toArray();

    const formattedInvites = invites.map(invite => ({
      id: invite.id,
      email: invite.recipientEmail,
      projectId: invite.projectId,
      status: invite.status,
      createdAt: invite.createdAt,
      sentAt: invite.sentAt,
      viewedAt: invite.viewedAt
    }));

    return NextResponse.json({ invites: formattedInvites });

  } catch (error) {
    console.error('Error fetching invites:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
