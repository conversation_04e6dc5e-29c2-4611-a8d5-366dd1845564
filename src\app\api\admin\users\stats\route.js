import { NextResponse } from 'next/server'
import { auth } from '@/auth'
import UserService from '@/libs/userSchema'

// GET /api/admin/users/stats - Get user statistics
export async function GET() {
  try {
    const session = await auth()
    
    // Check if user is authenticated and is admin
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      )
    }

    const stats = await UserService.getUserStats()
    
    return NextResponse.json(stats)
  } catch (error) {
    console.error('Error in GET /api/admin/users/stats:', error)
    return NextResponse.json(
      { error: 'Failed to fetch user statistics' },
      { status: 500 }
    )
  }
}
