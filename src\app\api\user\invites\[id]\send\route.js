import { auth } from "@/auth";
import { NextResponse } from "next/server";
import { MongoClient } from "mongodb";

const uri = process.env.MONGODB_URI;

async function getDatabase() {
  const client = new MongoClient(uri);
  await client.connect();
  return client.db('luyarisite');
}

export async function POST(request, { params }) {
  try {
    const session = await auth();
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;
    
    const db = await getDatabase();
    const invitesCollection = db.collection('invites');

    // Find the invite
    const invite = await invitesCollection.findOne({
      id: id,
      senderEmail: session.user.email
    });

    if (!invite) {
      return NextResponse.json({ error: 'Invite not found' }, { status: 404 });
    }

    if (invite.status === 'sent') {
      return NextResponse.json({ error: 'Invite already sent' }, { status: 400 });
    }

    // Create invite link
    const inviteLink = `${process.env.NEXTAUTH_URL}/invite/${invite.id}`;
    
    // Prepare email content
    const emailContent = {
      to: invite.recipientEmail,
      subject: `${invite.senderName} has shared a project with you`,
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Project Invitation</title>
          <style>
            body { font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif; margin: 0; padding: 0; background-color: #f8fafc; }
            .container { max-width: 600px; margin: 0 auto; background-color: white; }
            .header { background: linear-gradient(135deg, #1e293b 0%, #334155 100%); padding: 40px 30px; text-align: center; }
            .header h1 { color: white; margin: 0; font-size: 28px; font-weight: 300; letter-spacing: 1px; }
            .content { padding: 40px 30px; }
            .content h2 { color: #1e293b; font-size: 24px; font-weight: 300; margin-bottom: 20px; }
            .content p { color: #64748b; line-height: 1.6; margin-bottom: 20px; font-weight: 300; }
            .cta-button { display: inline-block; background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); color: white; text-decoration: none; padding: 15px 30px; border-radius: 5px; font-weight: 400; letter-spacing: 0.5px; text-transform: uppercase; font-size: 14px; margin: 20px 0; }
            .cta-button:hover { background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%); }
            .footer { background-color: #f1f5f9; padding: 30px; text-align: center; color: #64748b; font-size: 14px; font-weight: 300; }
            .divider { height: 1px; background-color: #e2e8f0; margin: 30px 0; }
          </style>
        </head>
        <body>
          <div class="container">
            <div class="header">
              <h1>luyari.</h1>
              <p style="color: #cbd5e1; margin: 10px 0 0 0; font-weight: 300; letter-spacing: 0.5px;">Architectural Visualization</p>
            </div>
            
            <div class="content">
              <h2>You've been invited to view a project</h2>
              
              <p>Hello,</p>
              
              <p><strong>${invite.senderName}</strong> has shared an architectural visualization project with you and would like you to take a look.</p>
              
              <p>Our team specializes in creating photorealistic architectural visualizations that help architects, designers, and developers communicate their vision with clarity and impact.</p>
              
              <div style="text-align: center; margin: 30px 0;">
                <a href="${inviteLink}" class="cta-button">View Project</a>
              </div>
              
              <p style="font-size: 14px; color: #94a3b8;">This invitation link will expire in 30 days. If you have any questions, please contact ${invite.senderName} directly.</p>
              
              <div class="divider"></div>
              
              <p style="font-size: 14px; color: #94a3b8;">
                If the button above doesn't work, you can copy and paste this link into your browser:<br>
                <a href="${inviteLink}" style="color: #3b82f6; word-break: break-all;">${inviteLink}</a>
              </p>
            </div>
            
            <div class="footer">
              <p>© ${new Date().getFullYear()} Luyari Architectural Visualization. All rights reserved.</p>
              <p>This email was sent because ${invite.senderName} invited you to view their project.</p>
            </div>
          </div>
        </body>
        </html>
      `
    };

    // Send email
    const emailResponse = await fetch(`${process.env.NEXTAUTH_URL}/api/send-email`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(emailContent)
    });

    if (!emailResponse.ok) {
      throw new Error('Failed to send email');
    }

    // Update invite status
    await invitesCollection.updateOne(
      { id: id },
      { 
        $set: { 
          status: 'sent',
          sentAt: new Date().toISOString()
        }
      }
    );

    return NextResponse.json({ 
      success: true, 
      message: 'Invite sent successfully',
      sentAt: new Date().toISOString()
    });

  } catch (error) {
    console.error('Error sending invite:', error);
    return NextResponse.json({ error: 'Failed to send invite' }, { status: 500 });
  }
}
