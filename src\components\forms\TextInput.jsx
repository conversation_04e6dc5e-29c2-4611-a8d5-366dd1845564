// src/components/forms/TextInput.jsx
// Reusable text input component for building forms

"use client";

import { useState } from 'react';

/**
 * TextInput Component
 * @param {Object} props
 * @param {string} props.name - Input name
 * @param {string} props.label - Input label
 * @param {string} props.value - Input value
 * @param {Function} props.onChange - Change handler
 * @param {string} props.placeholder - Placeholder text
 * @param {boolean} props.required - Is required field
 * @param {string} props.type - Input type (text, email, number, etc.)
 * @param {string} props.error - Error message
 * @param {string} props.helpText - Help text
 * @param {boolean} props.disabled - Is disabled
 * @param {number} props.maxLength - Maximum length
 * @param {boolean} props.multiline - Use textarea instead of input
 * @param {number} props.rows - Textarea rows (if multiline)
 * @param {string} props.autoComplete - Autocomplete attribute
 */
export default function TextInput({
  name,
  label,
  value = '',
  onChange,
  placeholder = '',
  required = false,
  type = 'text',
  error = '',
  helpText = '',
  disabled = false,
  maxLength = null,
  multiline = false,
  rows = 3,
  className = '',
  autoComplete = ''
}) {
  const [isFocused, setIsFocused] = useState(false);

  const handleChange = (e) => {
    if (onChange) {
      onChange(e.target.value, name);
    }
  };

  const inputClasses = `
    w-full px-4 py-3 border rounded-lg transition-all duration-200
    ${error 
      ? 'border-red-500 focus:border-red-500 focus:ring-red-200' 
      : isFocused 
        ? 'border-blue-500 focus:border-blue-500 focus:ring-blue-200' 
        : 'border-gray-300 hover:border-gray-400'
    }
    ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
    focus:outline-none focus:ring-2 focus:ring-opacity-50
    ${className}
  `.trim();

  const labelClasses = `
    block text-sm font-medium mb-2 transition-colors duration-200
    ${error ? 'text-red-700' : 'text-gray-700'}
    ${required ? "after:content-['*'] after:text-red-500 after:ml-1" : ''}
  `.trim();

  return (
    <div className="mb-4">
      {label && (
        <label htmlFor={name} className={labelClasses}>
          {label}
        </label>
      )}
      
      {multiline ? (
        <textarea
          id={name}
          name={name}
          value={value}
          onChange={handleChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          maxLength={maxLength}
          rows={rows}
          className={inputClasses}
        />
      ) : (
        <input
          id={name}
          name={name}
          type={type}
          value={value}
          onChange={handleChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          placeholder={placeholder}
          required={required}
          disabled={disabled}
          maxLength={maxLength}
          autoComplete={autoComplete}
          className={inputClasses}
        />
      )}

      {/* Character count for maxLength */}
      {maxLength && value && (
        <div className="text-xs text-gray-500 mt-1 text-right">
          {value.length}/{maxLength}
        </div>
      )}

      {/* Help text */}
      {helpText && !error && (
        <p className="text-sm text-gray-600 mt-1">{helpText}</p>
      )}

      {/* Error message */}
      {error && (
        <p className="text-sm text-red-600 mt-1 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {error}
        </p>
      )}
    </div>
  );
}
