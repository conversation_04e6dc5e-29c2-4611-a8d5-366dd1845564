import { Html } from '@react-three/drei'
import React from 'react'

export default function LoadingSpinner() {
  return (
    <Html center >
      <div className="absolute m-auto bg-black/50 z-50 top-0 left-0 w-full h-full flex items-center justify-center font-inter text-gray-900 antialiased">
        <div className="flex items-center justify-center w-fit h-fit">
          <div className="lg:w-16 w-12 lg:h-16 h-12 border-6 border-gray-200 border-t-gray-600 rounded-full spinner-tailwind"></div>
        </div>
      </div>

      {/* <div className="absolute m-auto w-full h-full top-0 left-0 flex items-center justify-center bg-black bg-opacity-75 z-50">
        <div className="flex w-fit h-fit flex-col items-center">
          <div className="animate-spin rounded-full h-16 w-16 border-t-4 border-b-4 border-white"></div>
          <p className="mt-4 text-white text-lg">Loading 3D experience...</p>
        </div>
      </div> */}
    </Html>
  )
}
