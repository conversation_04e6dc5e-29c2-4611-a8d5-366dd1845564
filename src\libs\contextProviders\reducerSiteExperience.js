// export const INITIAL_SITE_EXP_STATE={
//     controls:false,
//     mode360:true,
//     modeAR:false,
//     orbitControlMode:true,
//     modeModel:false,
//     isARSupported:false,
//     arSession:false,
//     editModeSettings:false,
//     level:0,
//     roomSnapPosition:'',
//     textureIndex:0,
//     maxDistance:80,
//     minDistance:40,
//     position:[],
//     positionAR:[],
//     domElement:{},
// }
export const INITIAL_SITE_EXP_STATE={
    // ======DEFAULT EXPERIENCE SETTINGS======
    orbitControlMode:false,
    mode360:true,
    firstPersonView:true,
    modeModel:false,
    toggleARStartEnd:false,
    modeModelReset:false,
    modeAR:false,
    arSession:false,
    editModeSettings:false,
    roomSnapName:'',
    viewName:'',

    showHideLevel:false,
    showRoomSnaps:false,
    showColors:false,
    
    // ======EXPERIENCE SUPPORT SETTINGS======
    isARSupported:false,
    isWebGlSupported:false,
    
    // ======EXPERIENCE MENU SETTINGS======
    level:0,
    roomSnapPosition:'',
}

export const ACTIONS_EXP_SITE={
    TOGGLE_360:'TOGGLE_360',
    TOGGLE_MODEL:'TOGGLE_MODEL',
    TOGGLE_AR:'TOGGLE_AR',
    TOGGLE_AR_START_END:'TOGGLE_AR_START_END',
    TOGGLE_AR_OFF:'TOGGLE_AR_OFF',
    EDIT_MODEL:'EDIT_MODEL',
    AR_SUPPORT:'AR_SUPPORT',
    WEGgL_SUPPORT:'WEGgL_SUPPORT',
    
    SET_VIEW:'SET_VIEW',
    HIDE_LEVEL:'HIDE_LEVEL',
    ROOM_SNAP_NAME:'ROOM_SNAP_NAME',

    ENABLE_FPV:'ENABLE_FPV', 
    DISABLE_FPV:'DISABLE_FPV', 
    ENABLE_CONTROLS:'ENABLE_CONTROLS', 
    DISABLE_CONTROLS:'DISABLE_CONTROLS', 

    DOM_ELEMENT:'DOM_ELEMENT',
    AR_SESSION_MODE:'AR_SESSION_MODE',
    RESET:'RESET',
    MODEL_RESET:'MODEL_RESET',
    EDIT_MODEL_OFF:'EDIT_MODEL_OFF',
}

export const reducerSiteExperience=(state,action)=>{
    switch (action.type) {
        case 'TOGGLE_360':
            return{
                ...state,
                orbitControlMode:true,
                mode360:true,
                firstPersonView:true,
                modeModel:false,
                modeAR:false,
            }
        case 'ENABLE_CONTROLS':
            return{
                ...state,
                orbitControlMode:true,
                // modeAR:false,
            }
        case 'DISABLE_CONTROLS':
            return{
                ...state,
                orbitControlMode:false,
                // modeAR:false,
            }
        case 'ENABLE_FPV':
            return{
                ...state,
                firstPersonView:true,
                // modeAR:false,
            }
        case 'DISABLE_FPV':
            return{
                ...state,
                firstPersonView:false,
                // modeAR:false,
            }
        case 'TOGGLE_MODEL':
            return{
                ...state,
                orbitControlMode:true,
                mode360:false,
                firstPersonView:false,
                modeModel:true,
                // modeAR:false,
            }
         
        case 'TOGGLE_AR':
            return{
                ...state,
                modeAR:!state.modeAR,
                orbitControlMode:!state.orbitControlMode,
            }
         
        case 'TOGGLE_AR_START_END':
            return{
                ...state,
                toggleARStartEnd:!state.toggleARStartEnd,
            }
        case 'AR_SUPPORT':
            return{
                ...state,
                isARSupported:action.payload,
            }
        case 'WEGgL_SUPPORT':
            return{
                ...state,
                isWebGlSupported:action.payload,
            }
        case 'SET_VIEW':
            return{
                ...state,
                modeModelReset:false,
                viewName:action.payload,
            }
        case 'HIDE_LEVEL':
            return{
                ...state,
                showHideLevel:true,
                modeModelReset:false,
                level:action.payload,
            }
    
        case 'MODEL_RESET':
            return{
                ...state,
                showHideLevel:false,
                modeModel:true,
                mode360:false,
                firstPersonView:false,
                orbitControlMode:true,
                modeModelReset:true,
            }
        case 'EDIT_MODEL':
            return{
                ...state,
                modeModel:false,
                editModeSettings:true,
            }
        case 'ROOM_SNAP_NAME':
            return{
                ...state,
                enableRoomSnaps:true,
                firstPersonView:true,
                orbitControlMode:false,
                modeModelReset:false,
                roomSnapName:action.payload,
            }
        case 'RESET':
            return{
                ...state,
                orbitControlMode:true,
                mode360:true,
                modeModel:false,
                modeAR:false,
                arSession:false,
                editModeSettings:false,
                level:0,
            }
        default:
                state
            break;
    }
}