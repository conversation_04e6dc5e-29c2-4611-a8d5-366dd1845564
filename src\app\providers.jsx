"use client";

import { SessionProvider } from "next-auth/react";
import SiteContextProvider from "@/libs/contextProviders/useSiteContext";
import SiteExperienceContextProvider from "@/libs/contextProviders/useSiteExperienceContext";
import ExperienceContextProvider from "@/libs/contextProviders/useExperienceContext";

export function Providers({ children, session }) {
  return (
    <SessionProvider session={session}>
      <SiteContextProvider>
        <ExperienceContextProvider>
          <SiteExperienceContextProvider>
            {children}
          </SiteExperienceContextProvider>
        </ExperienceContextProvider>
      </SiteContextProvider>
    </SessionProvider>
  );
}
