@import "tailwindcss";

/* Font family configuration */
:root {
  --font-inter: 'Inter', system-ui, -apple-system, sans-serif;
  --font-jetbrains-mono: 'JetBrains Mono', 'Courier New', monospace;
}

/* Base font styles */
body {
  font-family: var(--font-inter);
}

.font-mono {
  font-family: var(--font-jetbrains-mono);
}

@keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
}

/* Apply the custom animation to the spinner class */
.spinner-tailwind {
  animation: spin 1s linear infinite;
}

/* Chaumet-inspired design system */
.chaumet-button {
  @apply relative inline-block px-8 py-3 border border-current text-xs font-light tracking-widest uppercase transition-all duration-500 overflow-hidden;
}

.chaumet-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: currentColor;
  transition: left 0.5s ease;
  z-index: 1;
}

.chaumet-button:hover::before {
  left: 0;
}

.chaumet-heading {
  @apply font-thin tracking-wide;
}

.chaumet-divider {
  @apply h-px bg-neutral-300 mx-auto mb-8;
}

/* Profile modal styles */
.profile-modal-overlay {
  @apply fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4;
}

.profile-modal {
  @apply bg-white rounded-lg shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto;
}

/* Form input styles matching the design */
.chaumet-input {
  @apply w-full px-4 py-3 border border-neutral-200 focus:border-neutral-400 focus:outline-none transition-colors duration-300 font-light;
}

.chaumet-select {
  @apply w-full px-4 py-3 border border-neutral-200 focus:border-neutral-400 focus:outline-none transition-colors duration-300 font-light bg-white;
}

.chaumet-textarea {
  @apply w-full px-4 py-3 border border-neutral-200 focus:border-neutral-400 focus:outline-none transition-colors duration-300 font-light resize-none;
}