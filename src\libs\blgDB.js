export const buildingDB=[
  {
    _id: 1,
    projectTitle: "G-North",
    siteSection: "featured",
    price: "2000",
    buildingTitle: "3 bedrooed house",
    buildingType: "single-storey",
    desc: "The brief was to come with a cozy 4 bedroom house with a guest room attached. It also had to have a pajama lounge for the viewing television as well as a separate formal lounge which would be used to entertain quests. The design needed to be trendy but understated. It need to sophisticated enough but affordable to build",
    buildingSummary: {
      "area": "235",
      "beds": "4",
      "level": "1",
      "baths": "3",
      "cars": "2"
    },
    features: "FEATURES: 4 Bedrooms, a Guest Bedroom, Breakfast - nook, Pajama-lounge, Scullery, formal dining, Office space, outdoor seating and entertainment area, and an airy kitchen",
    outroSection: "The design celebrates the life style of the client and as occurred as the plot was the challenge was truly inspirational",
    buildingHighlights: [
      {
        "id": 0,
        "title": "PLAN DIMENSIONS",
        "desc": "235M2 Area: 16.56m X 23.69m,Height: 5.6m"
      },
      {
        "id": 1,
        "title": "CEILING HEIGHTS",
        "desc": "2.7meters"
      },
      {
        "id": 2,
        "title": "SQUARE FOOTAGE BREAKDOWN",
        "desc": "Plinth Area: 235m2, Porch Area: 40m2"
      },
      {
        "id": 3,
        "title": "BEDROOMS",
        "desc": "1 Master Bedroom: 4.8m X 4.6m, Walking Closet: 1.38m X 4.6m, Sleeping Area: 3.35m X 4.6m, 2 Bathrooms: 3.33m X 2.99m"
      },
      {
        "id": 4,
        "title": "ADDITIONAL ROOMS",
        "desc": "Garage Area: 5.98m X 6.16m, 2 Parking"
      },
      {
        "id": 5,
        "title": "OUTDOOR AREAS",
        "desc": "Porch: 40m2"
      },
      {
        "id": 6,
        "title": "KITCHEN",
        "desc": "Kitchen Area: 2.99m X 5.26m, Island: 0.9m X 1.2m"
      }
    ],
    renders: [
      {
        id: 1,
        url: "/ms mudenda/0001.jpg",
        name: "0006",
        desertRefPath: "luyaridesigns/ms mudenda/0001.jpg"
      },
      {
        id: 2,
        url: "/ms mudenda/0002.jpg",
        name: "0001",
        desertRefPath: "luyaridesigns/ms mudenda/0002.jpg"
      },
      {
        id: 3,
        url: "/ms mudenda/0003.jpg",
        name: "0005",
        desertRefPath: "luyaridesigns/ms mudenda/0003.jpg"
      },
      {
        id: 4,
        url: "/ms mudenda/0004.jpg",
        name: "0004",
        desertRefPath: "luyaridesigns/ms mudenda/0004.jpg"
      },
      {
        id: 5,
        url: "/ms mudenda/0006.jpg",
        name: "0002",
        desertRefPath: "luyaridesigns/ms mudenda/0002.jpg"
      },
      {
        id: 6,
        url: "/ms mudenda/0006.jpg",
        name: "0003",
        desertRefPath: "luyaridesigns/ms mudenda/0003.jpg"
      }
    ],
    drawings: [
      {
        id: 1,
        url: "/ms mudenda/Elevations.jpg",
        name: "Elevations",
        desertRefPath: "luyaridesigns/ms mudenda/Elevations.jpg"
      },
      {
        id: 2,
        url: "/ms mudenda/floor-plans.jpg",
        name: "floor-plans",
        desertRefPath: "luyaridesigns/ms mudenda/floor-plans.jpg"
      },
    ],
    modelsFiles: [
      {
        id: 1,
        url: "/ms mudenda/grd floor.glb",
        name: "house_Opt_grd",
        desertRefPath: "luyaridesigns/Gnorth/house_Opt_grd.glb"
      }
    ],
    hideLevel: [
      {
        id: 1,
        url: "/ms mudenda/1st floor.glb",
        name: "1st floor",
        desertRefPath: "luyaridesigns/Gnorth/house_Opt_roof.glb"
      },
      {
        id: 2,
        url: "/ms mudenda/roof.glb",
        name: "roof",
        desertRefPath: "luyaridesigns/Gnorth/house_Opt_roof.glb"
      }
    ],
    supportFiles: [
      {
        id: 1,
        url: "/ms mudenda/Site_final.glb",
        name: "Site final",
        desertRefPath: "luyaridesigns/Gnorth/house_Opt_site.glb"
      }
    ],
    _360sImages: [
      {
        id: 1,
        url: "/ms mudenda/Opt1_360_0000.jpg",
        name: "360 pool view",
        desertRefPath: "luyaridesigns/Gnorth/360 front view.jpg"
      },
      {
        id: 2,
        url: "/ms mudenda/Opt1_360_0001.jpg",
        name: "360 front view",
        desertRefPath: "luyaridesigns/Gnorth/360 pool view.jpg"
      }
    ],
    position:'-8,0,13',
    arPosition:'-8,0,13',
    minDistance: "30",
    maxDistance: "60",
    roomSnaps: [
      {
        id: 1,
        url: "/ms mudenda/1 front view 1.glb",
        name: "1 front view 1",
        desertRefPath: "luyaridesigns/ms mudenda/1 front view 1.glb"
      },
      {
        id: 2,
        url: "/ms mudenda/2 front view 2.glb",
        name: "2 front view 2",
        desertRefPath: "luyaridesigns/ms mudenda/2 front view 2.glb"
      },
      {
        id: 3,
        url: "/ms mudenda/3 pool view.glb",
        name: "3 pool view",
        desertRefPath: "luyaridesigns/ms mudenda/2 front view 2.glb"
      },
    ],
    presentationDrawings: [
      {
        id: 1,
        url: "https://firebasestorage.googleapis.com/v0/b/luyaridesigns-562cd.appspot.com/o/luyaridesigns%2FGnorth%2FFinal%20Brochure.pdf?alt=media&token=10c8d672-f9d9-48b7-b5b2-cba16bebe103",
        name: "Final Brochure",
        desertRefPath: "luyaridesigns/Gnorth/Final Brochure.pdf"
      }
    ],
    constructionDrawings: [
      {
        id: 1,
        url: "https://firebasestorage.googleapis.com/v0/b/luyaridesigns-562cd.appspot.com/o/luyaridesigns%2FGnorth%2FMr%20Masilo%20Drawings%20Rev1.pdf?alt=media&token=c8936de8-ca48-446a-a599-f9dccb20d6d6",
        name: "Mr Masilo Drawings Rev1",
        desertRefPath: "luyaridesigns/Gnorth/Mr Masilo Drawings Rev1.pdf"
      }
    ],
    createdAt: {
      "$date": "2024-06-13T10:27:06.191Z"
    },
    updatedAt: {
      "$date": "2024-06-13T10:27:06.191Z"
    }
  },

  {
    _id: 2,
    projectTitle: "TheMontes",
    siteSection: "featured",
    price: "2500",
    buildingTitle: "4 bedrooed house",
    buildingType: "single-storey",
    desc: "The brief was to come with a cozy 4 bedroom house with a guest room attached. It also had to have a pajama lounge for the viewing television as well as a separate formal lounge which would be used to entertain quests. The design needed to be trendy but understated. It need to sophisticated enough but affordable to build",
    buildingSummary: {
      "area": "235",
      "beds": "4",
      "level": "1",
      "baths": "3",
      "cars": "2"
    },
    features: "FEATURES: 4 Bedrooms, a Guest Bedroom, Breakfast - nook, Pajama-lounge, Scullery, formal dining, Office space, outdoor seating and entertainment area, and an airy kitchen",
    outroSection: "The design celebrates the life style of the client and as occurred as the plot was the challenge was truly inspirational",
    buildingHighlights: [
      {
        "id": 0,
        "title": "PLAN DIMENSIONS",
        "desc": "235M2 Area: 16.56m X 23.69m,Height: 5.6m"
      },
      {
        "id": 1,
        "title": "CEILING HEIGHTS",
        "desc": "2.7meters"
      },
      {
        "id": 2,
        "title": "SQUARE FOOTAGE BREAKDOWN",
        "desc": "Plinth Area: 235m2, Porch Area: 40m2"
      },
      {
        "id": 3,
        "title": "BEDROOMS",
        "desc": "1 Master Bedroom: 4.8m X 4.6m, Walking Closet: 1.38m X 4.6m, Sleeping Area: 3.35m X 4.6m, 2 Bathrooms: 3.33m X 2.99m"
      },
      {
        "id": 4,
        "title": "ADDITIONAL ROOMS",
        "desc": "Garage Area: 5.98m X 6.16m, 2 Parking"
      },
      {
        "id": 5,
        "title": "OUTDOOR AREAS",
        "desc": "Porch: 40m2"
      },
      {
        "id": 6,
        "title": "KITCHEN",
        "desc": "Kitchen Area: 2.99m X 5.26m, Island: 0.9m X 1.2m"
      }
    ],
    renders: [
      {
        id: 1,
        url: "/the_monts/View_1-Lawn-Option-1.jpg",
        name: "View 1",
        desertRefPath: "luyaridesigns/the_monts/View 6.jpg"
      },
      {
        id: 2,
        url: "/the_monts/View_2-Driveway-Option-1.jpg",
        name: "View 2",
        desertRefPath: "luyaridesigns/the_monts/View 1.jpg"
      },
      {
        id: 3,
        url: "/the_monts/View_3-Pool-Option-2.jpg",
        name: "View 5",
        desertRefPath: "luyaridesigns/the_monts/View 5.jpg"
      },
      {
        id: 4,
        url: "/the_monts/0005.jpg",
        name: "View 4",
        desertRefPath: "luyaridesigns/the_monts/View 4.jpg"
      },
      {
        id: 5,
        url: "/the_monts/0006.jpg",
        name: "View 2",
        desertRefPath: "luyaridesigns/the_monts/View 2.jpg"
      },
      {
        id: 6,
        url: "/the_monts/0008.jpg",
        name: "View 3",
        desertRefPath: "luyaridesigns/the_monts/View 3.jpg"
      }
    ],
    drawings: [
      {
        id: 1,
        url: "/the_monts/Opt 1 Plans.jpg",
        name: "Elevations",
        desertRefPath: "luyaridesigns/the_monts/Elevations.jpg"
      },
      {
        id: 2,
        url: "/the_monts/Opt 1 Elevation.jpg",
        name: "floor-plans",
        desertRefPath: "luyaridesigns/the_monts/floor-plans.jpg"
      },
    ],
    modelsFiles: [
      {
        id: 1,
        url: "/the_monts/Montle Blg grd.glb",
        name: "house_Opt_grd",
        desertRefPath: "luyaridesigns/the_monts/Montle Blg grd.glb"
      }
    ],
    hideLevel: [
      {
        id: 1,
        url: "/the_monts/Montle Blg grd.glb",
        name: "house_Opt_roof",
        desertRefPath: "luyaridesigns/Gnorth/house_Opt_roof.glb"
      }
    ],
    supportFiles: [
      {
        id: 1,
        url: "/the_monts/Montle Blg grd.glb",
        name: "house_Opt_site",
        desertRefPath: "luyaridesigns/Gnorth/house_Opt_site.glb"
      }
    ],
    _360sImages: [
      {
        id: 1,
        url: "/the_monts/Opt1_360_0001.jpg",
        name: "360 pool view",
        desertRefPath: "luyaridesigns/Gnorth/360 pool view.jpg"
      },
      {
        id: 2,
        url: "/the_monts/Opt2_360_0002.jpg",
        name: "360 front view",
        desertRefPath: "luyaridesigns/Gnorth/360 front view.jpg"
      }
    ],
    position:'-8,0,13',
    arPosition:'-8,0,13',
    minDistance: "30",
    maxDistance: "60",
    roomSnaps: [

    ],
    presentationDrawings: [
      {
        "id": 1,
        "url": "https://firebasestorage.googleapis.com/v0/b/luyaridesigns-562cd.appspot.com/o/luyaridesigns%2FGnorth%2FFinal%20Brochure.pdf?alt=media&token=10c8d672-f9d9-48b7-b5b2-cba16bebe103",
        "name": "Final Brochure",
        "desertRefPath": "luyaridesigns/Gnorth/Final Brochure.pdf"
      }
    ],
    constructionDrawings: [
      {
        "id": 1,
        "url": "https://firebasestorage.googleapis.com/v0/b/luyaridesigns-562cd.appspot.com/o/luyaridesigns%2FGnorth%2FMr%20Masilo%20Drawings%20Rev1.pdf?alt=media&token=c8936de8-ca48-446a-a599-f9dccb20d6d6",
        "name": "Mr Masilo Drawings Rev1",
        "desertRefPath": "luyaridesigns/Gnorth/Mr Masilo Drawings Rev1.pdf"
      }
    ],
    createdAt: {
      "$date": "2024-06-13T10:27:06.191Z"
    },
    updatedAt: {
      "$date": "2024-06-13T10:27:06.191Z"
    }
  },

  {
    _id: 3,
    projectTitle: "Sifelane",
    siteSection: "featured",
    price: "2500",
    buildingTitle: "5 bedrooed house",
    buildingType: "multi-storey",
    desc: "The brief was to come with a cozy 4 bedroom house with a guest room attached. It also had to have a pajama lounge for the viewing television as well as a separate formal lounge which would be used to entertain quests. The design needed to be trendy but understated. It need to sophisticated enough but affordable to build",
    buildingSummary: {
      "area": "600",
      "beds": "5",
      "level": "3",
      "baths": "5",
      "cars": "4"
    },
    features: "FEATURES: 4 Bedrooms, a Guest Bedroom, Breakfast - nook, Pajama-lounge, Scullery, formal dining, Office space, outdoor seating and entertainment area, and an airy kitchen",
    outroSection: "The design celebrates the life style of the client and as occurred as the plot was the challenge was truly inspirational",
    buildingHighlights: [
      {
        "id": 0,
        "title": "PLAN DIMENSIONS",
        "desc": "235M2 Area: 16.56m X 23.69m,Height: 5.6m"
      },
      {
        "id": 1,
        "title": "CEILING HEIGHTS",
        "desc": "2.7meters"
      },
      {
        "id": 2,
        "title": "SQUARE FOOTAGE BREAKDOWN",
        "desc": "Plinth Area: 235m2, Porch Area: 40m2"
      },
      {
        "id": 3,
        "title": "BEDROOMS",
        "desc": "1 Master Bedroom: 4.8m X 4.6m, Walking Closet: 1.38m X 4.6m, Sleeping Area: 3.35m X 4.6m, 2 Bathrooms: 3.33m X 2.99m"
      },
      {
        "id": 4,
        "title": "ADDITIONAL ROOMS",
        "desc": "Garage Area: 5.98m X 6.16m, 2 Parking"
      },
      {
        "id": 5,
        "title": "OUTDOOR AREAS",
        "desc": "Porch: 40m2"
      },
      {
        "id": 6,
        "title": "KITCHEN",
        "desc": "Kitchen Area: 2.99m X 5.26m, Island: 0.9m X 1.2m"
      }
    ],
    renders: [
      {
        id: 1,
        url: "/Sifelane/0001.jpg",
        name: "View 6",
        desertRefPath: "luyaridesigns/Sifelane/View 6.jpg"
      },
      {
        id: 2,
        url: "/Sifelane/0002.jpg",
        name: "View 6",
        desertRefPath: "luyaridesigns/Sifelane/View 6.jpg"
      },
      {
        id: 3,
        url: "/Sifelane/0003.jpg",
        name: "View 6",
        desertRefPath: "luyaridesigns/Sifelane/View 6.jpg"
      },
      {
        id: 4,
        url: "/Sifelane/0004.jpg",
        name: "View 6",
        desertRefPath: "luyaridesigns/Sifelane/View 6.jpg"
      },
      {
        id: 5,
        url: "/Sifelane/0005.jpg",
        name: "View 6",
        desertRefPath: "luyaridesigns/Sifelane/View 6.jpg"
      },
      {
        id: 6,
        url: "/Sifelane/0006.jpg",
        name: "View 6",
        desertRefPath: "luyaridesigns/Sifelane/View 6.jpg"
      },
      {
        id: 7,
        url: "/Sifelane/0007.jpg",
        name: "View 6",
        desertRefPath: "luyaridesigns/Sifelane/View 6.jpg"
      },
    ],
    drawings: [
      {
        id: 1,
        url: "/Sifelane/elevations.jpg",
        name: "Elevations",
        desertRefPath: "luyaridesigns/Sifelane/Elevations.jpg"
      },
      {
        id: 2,
        url: "/Sifelane/plans.jpg",
        name: "floor-plans",
        desertRefPath: "luyaridesigns/Sifelane/floor-plans.jpg"
      },
    ],
    modelsFiles: [
      {
        id: 1,
        url: "/Sifelane/blg_grd.glb",
        name: "blg_grd",
        desertRefPath: "luyaridesigns/Sifelane/blg_grd.glb"
      },
    ],
    hideLevel: [
      {
        id: 1,
        url: "/Sifelane/blg_1st.glb",
        name: "blg_1st",
        desertRefPath: "luyaridesigns/Sifelane/house_Opt_grd.glb"
      },
      {
        id: 2,
        url: "/Sifelane/blg_2nd.glb",
        name: "blg_2nd",
        desertRefPath: "luyaridesigns/Sifelane/house_Opt_grd.glb"
      },
      {
        id: 3,
        url: "/Sifelane/blg_roof.glb",
        name: "blg_roof",
        desertRefPath: "luyaridesigns/Sifelane/house_Opt_grd.glb"
      },
    ],
    supportFiles: [
      {
        id: 0,
        url: "/Sifelane/site fence.glb",
        name: "site_final",
        desertRefPath: "luyaridesigns/Sifelane/house_Opt_site.glb"
      },
      {
        id: 1,
        url: "/Sifelane/site_final.glb",
        name: "site_final",
        desertRefPath: "luyaridesigns/Sifelane/house_Opt_site.glb"
      }
    ],
    _360sImages: [
      {
        id: 1,
        url: "/the_monts/Opt1_360_0001.jpg",
        name: "360 pool view",
        desertRefPath: "luyaridesigns/Gnorth/360 pool view.jpg"
      },
      {
        id: 2,
        url: "/the_monts/Opt2_360_0002.jpg",
        name: "360 front view",
        desertRefPath: "luyaridesigns/Gnorth/360 front view.jpg"
      }
    ],
    position:'-14,0,7',
    arPosition:'-8,0,13',
    minDistance: "30",
    maxDistance: "60",
    roomSnaps: [
      {
        "id": 1,
        "url": "/Sifelane/pos1.glb",
        "name": "pos1",
        "desertRefPath": "luyaridesigns/Gnorth/pos1.glb"
      },
      {
        "id": 2,
        "url": "/Sifelane/pos2.glb",
        "name": "pos2",
        "desertRefPath": "luyaridesigns/Gnorth/pos1.glb"
      },
      {
        "id": 3,
        "url": "/Sifelane/pos3.glb",
        "name": "pos3",
        "desertRefPath": "luyaridesigns/Gnorth/pos1.glb"
      },
      {
        "id": 4,
        "url": "/Sifelane/pos4.glb",
        "name": "pos4",
        "desertRefPath": "luyaridesigns/Gnorth/pos1.glb"
      },
      {
        "id": 5,
        "url": "/Sifelane/pos5.glb",
        "name": "pos5",
        "desertRefPath": "luyaridesigns/Gnorth/pos1.glb"
      },
      {
        "id": 6,
        "url": "/Sifelane/pos6.glb",
        "name": "pos6",
        "desertRefPath": "luyaridesigns/Gnorth/pos1.glb"
      },
      {
        "id": 7,
        "url": "/Sifelane/pos7.glb",
        "name": "pos7",
        "desertRefPath": "luyaridesigns/Gnorth/pos1.glb"
      },
      {
        "id": 8,
        "url": "/Sifelane/pos8.glb",
        "name": "pos8",
        "desertRefPath": "luyaridesigns/Gnorth/pos1.glb"
      },
    ],
    presentationDrawings: [
      {
        "id": 1,
        "url": "https://firebasestorage.googleapis.com/v0/b/luyaridesigns-562cd.appspot.com/o/luyaridesigns%2FGnorth%2FFinal%20Brochure.pdf?alt=media&token=10c8d672-f9d9-48b7-b5b2-cba16bebe103",
        "name": "Final Brochure",
        "desertRefPath": "luyaridesigns/Gnorth/Final Brochure.pdf"
      }
    ],
    constructionDrawings: [
      {
        "id": 1,
        "url": "https://firebasestorage.googleapis.com/v0/b/luyaridesigns-562cd.appspot.com/o/luyaridesigns%2FGnorth%2FMr%20Masilo%20Drawings%20Rev1.pdf?alt=media&token=c8936de8-ca48-446a-a599-f9dccb20d6d6",
        "name": "Mr Masilo Drawings Rev1",
        "desertRefPath": "luyaridesigns/Gnorth/Mr Masilo Drawings Rev1.pdf"
      }
    ],
    createdAt: {
      "$date": "2024-06-13T10:27:06.191Z"
    },
    updatedAt: {
      "$date": "2024-06-13T10:27:06.191Z"
    }
  },

  // {
  //   _id: 4,
  //   projectTitle: "makeni",
  //   siteSection: "featured",
  //   price: "2500",
  //   buildingTitle: "2 bedrooed house",
  //   buildingType: "multi-residential",
  //   desc: "The brief was to come with a cozy 4 bedroom house with a guest room attached. It also had to have a pajama lounge for the viewing television as well as a separate formal lounge which would be used to entertain quests. The design needed to be trendy but understated. It need to sophisticated enough but affordable to build",
  //   buildingSummary: {
  //     "area": "300",
  //     "beds": "2",
  //     "level": "1",
  //     "baths": "2",
  //     "cars": "2"
  //   },
  //   features: "FEATURES: 4 Bedrooms, a Guest Bedroom, Breakfast - nook, Pajama-lounge, Scullery, formal dining, Office space, outdoor seating and entertainment area, and an airy kitchen",
  //   outroSection: "The design celebrates the life style of the client and as occurred as the plot was the challenge was truly inspirational",
  //   buildingHighlights: [
  //     {
  //       "id": 0,
  //       "title": "PLAN DIMENSIONS",
  //       "desc": "235M2 Area: 16.56m X 23.69m,Height: 5.6m"
  //     },
  //     {
  //       "id": 1,
  //       "title": "CEILING HEIGHTS",
  //       "desc": "2.7meters"
  //     },
  //     {
  //       "id": 2,
  //       "title": "SQUARE FOOTAGE BREAKDOWN",
  //       "desc": "Plinth Area: 235m2, Porch Area: 40m2"
  //     },
  //     {
  //       "id": 3,
  //       "title": "BEDROOMS",
  //       "desc": "1 Master Bedroom: 4.8m X 4.6m, Walking Closet: 1.38m X 4.6m, Sleeping Area: 3.35m X 4.6m, 2 Bathrooms: 3.33m X 2.99m"
  //     },
  //     {
  //       "id": 4,
  //       "title": "ADDITIONAL ROOMS",
  //       "desc": "Garage Area: 5.98m X 6.16m, 2 Parking"
  //     },
  //     {
  //       "id": 5,
  //       "title": "OUTDOOR AREAS",
  //       "desc": "Porch: 40m2"
  //     },
  //     {
  //       "id": 6,
  //       "title": "KITCHEN",
  //       "desc": "Kitchen Area: 2.99m X 5.26m, Island: 0.9m X 1.2m"
  //     }
  //   ],
  //   renders: [
  //     {
  //       id: 1,
  //       url: "/makeni/0001.jpg",
  //       name: "View 1",
  //       desertRefPath: "/makeni/View 6.jpg"
  //     },
  //     {
  //       id: 2,
  //       url: "/makeni/0002.jpg",
  //       name: "View 2",
  //       desertRefPath: "/makeni/View 6.jpg"
  //     },
  //     {
  //       id: 3,
  //       url: "/makeni/0003.jpg",
  //       name: "View 3",
  //       desertRefPath: "/makeni/View 6.jpg"
  //     },
  //     {
  //       id: 4,
  //       url: "/makeni/0004.jpg",
  //       name: "View 4",
  //       desertRefPath: "/makeni/View 6.jpg"
  //     },
  //     {
  //       id: 5,
  //       url: "/makeni/0005.jpg",
  //       name: "View 5",
  //       desertRefPath: "/makeni/View 6.jpg"
  //     },
  //     {
  //       id: 6,
  //       url: "/makeni/0006.jpg",
  //       name: "View 6",
  //       desertRefPath: "/makeni/View 6.jpg"
  //     },
  //     {
  //       id: 7,
  //       url: "/makeni/0007.jpg",
  //       name: "View 7",
  //       desertRefPath: "/makeni/View 6.jpg"
  //     },
  //     {
  //       id: 8,
  //       url: "/makeni/0008.jpg",
  //       name: "View 8",
  //       desertRefPath: "/makeni/View 6.jpg"
  //     },
  //     {
  //       id: 9,
  //       url: "/makeni/0009.jpg",
  //       name: "View 9",
  //       desertRefPath: "/makeni/View 6.jpg"
  //     },
  //     {
  //       id:10,
  //       url: "/makeni/0010.jpg",
  //       name: "View 10",
  //       desertRefPath: "/makeni/View 6.jpg"
  //     },
  //     {
  //       id:11,
  //       url: "/makeni/0011.jpg",
  //       name: "View 11",
  //       desertRefPath: "/makeni/View 6.jpg"
  //     },
  //     {
  //       id:12,
  //       url: "/makeni/0012.jpg",
  //       name: "View 12",
  //       desertRefPath: "/makeni/View 6.jpg"
  //     },
  //   ],
  //   drawings: [
  //     {
  //       id: 1,
  //       url: "/makeni/elevations.jpg",
  //       name: "Elevations",
  //       desertRefPath: "/makeni/Elevations.jpg"
  //     },
  //     {
  //       id: 2,
  //       url: "/makeni/plans.jpg",
  //       name: "plans",
  //       desertRefPath: "/makeni/floor-plans.jpg"
  //     },
  //   ],
  //   modelsFiles: [
  //     {
  //       id: 1,
  //       url: "/makeni/Blg Option 1.glb",
  //       name: "Blg Option 1",
  //       desertRefPath: "/makeni/blg_grd.glb"
  //     },
  //     {
  //       id: 2,
  //       url: "/makeni/Blg Option 2.glb",
  //       name: "Blg Option 2",
  //       desertRefPath: "/makeni/blg_grd.glb"
  //     },
  //   ],
  //   hideLevel: [

  //   ],
  //   supportFiles: [
  //     {
  //       id: 1,
  //       url: "/makeni/Site Option 1",
  //       name: "Site Option 1",
  //       desertRefPath: "/makeni/Site Option 1.glb"
  //     },
  //     {
  //       id: 1,
  //       url: "/makeni/Site Option 2",
  //       name: "Site Option 2",
  //       desertRefPath: "/makeni/Site Option 2.glb"
  //     }
  //   ],
  //   _360sImages: [
  //     {
  //       id: 1,
  //       url: "/makeni/Opt1_360_0001.jpg",
  //       name: "360 pool view",
  //       desertRefPath: "/Gnorth/360 pool view.jpg"
  //     },
  //     {
  //       id: 2,
  //       url: "/makeni/Opt2_360_0002.jpg",
  //       name: "360 front view",
  //       desertRefPath: "/Gnorth/360 front view.jpg"
  //     }
  //   ],
  //   modelSettings: {
  //     "position": [
  //       "-8",
  //       "0",
  //       "10"
  //     ],
  //     "arPosition": [
  //       "-8",
  //       "-1",
  //       "10"
  //     ],
  //     "minDistance": "30",
  //     "maxDistance": "60",
  //     "roomSnaps": [
  //       {
  //         "id": 1,
  //         "url": "https://firebasestorage.googleapis.com/v0/b/luyaridesigns-562cd.appspot.com/o/luyaridesigns%2FGnorth%2Fpos1.glb?alt=media&token=1ca62c22-a55f-4ff5-8ece-302d638af2b4",
  //         "name": "pos1",
  //         "desertRefPath": "luyaridesigns/Gnorth/pos1.glb"
  //       },
  //       {
  //         "id": 2,
  //         "url": "https://firebasestorage.googleapis.com/v0/b/luyaridesigns-562cd.appspot.com/o/luyaridesigns%2FGnorth%2Fpos5.glb?alt=media&token=44af370b-d311-42e3-a035-3901c816bf88",
  //         "name": "pos5",
  //         "desertRefPath": "luyaridesigns/Gnorth/pos5.glb"
  //       },
  //       {
  //         "id": 3,
  //         "url": "https://firebasestorage.googleapis.com/v0/b/luyaridesigns-562cd.appspot.com/o/luyaridesigns%2FGnorth%2Fpos6.glb?alt=media&token=d9f08ddf-10c2-410b-a7e6-46e59acd6a95",
  //         "name": "pos6",
  //         "desertRefPath": "luyaridesigns/Gnorth/pos6.glb"
  //       },
  //       {
  //         "id": 4,
  //         "url": "https://firebasestorage.googleapis.com/v0/b/luyaridesigns-562cd.appspot.com/o/luyaridesigns%2FGnorth%2Fpos4.glb?alt=media&token=67e60de2-f58f-42e7-9839-c2b1a1a532e2",
  //         "name": "pos4",
  //         "desertRefPath": "luyaridesigns/Gnorth/pos4.glb"
  //       },
  //       {
  //         "id": 5,
  //         "url": "https://firebasestorage.googleapis.com/v0/b/luyaridesigns-562cd.appspot.com/o/luyaridesigns%2FGnorth%2Fpos2.glb?alt=media&token=84bad561-c3ff-4796-8e0c-0d097fa58938",
  //         "name": "pos2",
  //         "desertRefPath": "luyaridesigns/Gnorth/pos2.glb"
  //       },
  //       {
  //         "id": 6,
  //         "url": "https://firebasestorage.googleapis.com/v0/b/luyaridesigns-562cd.appspot.com/o/luyaridesigns%2FGnorth%2Fpos3.glb?alt=media&token=6b4a64a1-26ab-4fd0-bdbe-a926a1c6357e",
  //         "name": "pos3",
  //         "desertRefPath": "luyaridesigns/Gnorth/pos3.glb"
  //       },
  //       {
  //         "id": 7,
  //         "url": "https://firebasestorage.googleapis.com/v0/b/luyaridesigns-562cd.appspot.com/o/luyaridesigns%2FGnorth%2Fpos7.glb?alt=media&token=ad2d9eae-8daa-48f2-b97c-3680d08fae32",
  //         "name": "pos7",
  //         "desertRefPath": "luyaridesigns/Gnorth/pos7.glb"
  //       }
  //     ]
  //   },
  //   presentationDrawings: [
  //     {
  //       "id": 1,
  //       "url": "https://firebasestorage.googleapis.com/v0/b/luyaridesigns-562cd.appspot.com/o/luyaridesigns%2FGnorth%2FFinal%20Brochure.pdf?alt=media&token=10c8d672-f9d9-48b7-b5b2-cba16bebe103",
  //       "name": "Final Brochure",
  //       "desertRefPath": "luyaridesigns/Gnorth/Final Brochure.pdf"
  //     }
  //   ],
  //   constructionDrawings: [
  //     {
  //       "id": 1,
  //       "url": "https://firebasestorage.googleapis.com/v0/b/luyaridesigns-562cd.appspot.com/o/luyaridesigns%2FGnorth%2FMr%20Masilo%20Drawings%20Rev1.pdf?alt=media&token=c8936de8-ca48-446a-a599-f9dccb20d6d6",
  //       "name": "Mr Masilo Drawings Rev1",
  //       "desertRefPath": "luyaridesigns/Gnorth/Mr Masilo Drawings Rev1.pdf"
  //     }
  //   ],
  //   createdAt: {
  //     "$date": "2024-06-13T10:27:06.191Z"
  //   },
  //   updatedAt: {
  //     "$date": "2024-06-13T10:27:06.191Z"
  //   }
  // }
]