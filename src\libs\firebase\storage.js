// src/libs/firebase/storage.js
// Firebase Storage utilities with offline fallback

import { storage } from './firebaseIntergration';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';
import { v4 as uuidv4 } from 'uuid';

/**
 * Upload file to Firebase Storage with offline fallback
 * @param {File} file - The file to upload
 * @param {string} projectTitle - Project title for folder structure
 * @param {string} category - File category (renders, drawings, etc.)
 * @param {number} priority - Priority for hideLevel files
 * @returns {Promise<Object>} Upload result with URL and metadata
 */
export async function uploadFile(file, projectTitle, category, priority = null) {
  try {
    // Validate inputs
    if (!file || !projectTitle || !category) {
      throw new Error('Missing required parameters: file, projectTitle, or category');
    }

    // Generate unique filename
    const fileId = uuidv4();
    const fileExtension = file.name.split('.').pop();
    const fileName = `${fileId}.${fileExtension}`;
    
    // Create Firebase storage path
    const storagePath = `luyari/${projectTitle}/${category}/${fileName}`;
    
    try {
      // Try Firebase upload first
      const storageRef = ref(storage, storagePath);
      const snapshot = await uploadBytes(storageRef, file);
      const firebaseUrl = await getDownloadURL(snapshot.ref);
      
      return {
        success: true,
        id: fileId,
        name: file.name,
        url: firebaseUrl,
        firebaseUrl: firebaseUrl,
        size: file.size,
        type: file.type,
        priority: priority,
        storagePath: storagePath,
        uploadMethod: 'firebase'
      };
      
    } catch (firebaseError) {
      console.warn('Firebase upload failed, falling back to local storage:', firebaseError);
      
      // Fallback to local storage
      const localUrl = await uploadToLocal(file, projectTitle, category, fileId);
      
      return {
        success: true,
        id: fileId,
        name: file.name,
        url: localUrl,
        firebaseUrl: null,
        size: file.size,
        type: file.type,
        priority: priority,
        storagePath: null,
        uploadMethod: 'local'
      };
    }
    
  } catch (error) {
    console.error('File upload error:', error);
    return {
      success: false,
      error: error.message
    };
  }
}

/**
 * Upload file to local uploads folder (offline fallback)
 * @param {File} file - The file to upload
 * @param {string} projectTitle - Project title for folder structure
 * @param {string} category - File category
 * @param {string} fileId - Unique file ID
 * @returns {Promise<string>} Local file URL
 */
async function uploadToLocal(file, projectTitle, category, fileId) {
  // Create FormData for local upload
  const formData = new FormData();
  formData.append('file', file);
  formData.append('projectTitle', projectTitle);
  formData.append('category', category);
  formData.append('fileId', fileId);
  
  // Upload to local API endpoint
  const response = await fetch('/api/upload/local', {
    method: 'POST',
    body: formData
  });
  
  if (!response.ok) {
    throw new Error('Local upload failed');
  }
  
  const result = await response.json();
  return result.url;
}

/**
 * Delete file from Firebase Storage
 * @param {string} storagePath - Firebase storage path
 * @returns {Promise<boolean>} Success status
 */
export async function deleteFile(storagePath) {
  try {
    if (!storagePath) {
      return false;
    }
    
    const storageRef = ref(storage, storagePath);
    await deleteObject(storageRef);
    return true;
    
  } catch (error) {
    console.error('Error deleting file from Firebase:', error);
    return false;
  }
}

/**
 * Upload multiple files
 * @param {FileList} files - Files to upload
 * @param {string} projectTitle - Project title
 * @param {string} category - File category
 * @param {Array} priorities - Priorities for hideLevel files
 * @returns {Promise<Array>} Upload results
 */
export async function uploadMultipleFiles(files, projectTitle, category, priorities = []) {
  const uploadPromises = Array.from(files).map((file, index) => {
    const priority = priorities[index] || null;
    return uploadFile(file, projectTitle, category, priority);
  });
  
  return Promise.all(uploadPromises);
}

/**
 * Validate file type based on category
 * @param {File} file - File to validate
 * @param {string} category - Expected category
 * @returns {boolean} Is valid
 */
export function validateFileType(file, category) {
  const fileExtension = file.name.split('.').pop().toLowerCase();
  
  const categoryTypes = {
    renders: ['jpg', 'jpeg', 'png', 'webp'],
    drawings: ['jpg', 'jpeg', 'png', 'webp'],
    _360sImages: ['jpg', 'jpeg', 'png', 'webp'],
    modelsFiles: ['glb'],
    hideLevel: ['glb'],
    supportFiles: ['glb'],
    roomSnaps: ['glb'],
    presentationDrawings: ['pdf'],
    constructionDrawingsPdf: ['pdf'],
    constructionDrawingsDwg: ['dwg']
  };
  
  const allowedTypes = categoryTypes[category];
  return allowedTypes ? allowedTypes.includes(fileExtension) : false;
}

/**
 * Get file size limit based on category
 * @param {string} category - File category
 * @returns {number} Size limit in bytes
 */
export function getFileSizeLimit(category) {
  const sizeLimits = {
    renders: 10 * 1024 * 1024, // 10MB
    drawings: 10 * 1024 * 1024, // 10MB
    _360sImages: 15 * 1024 * 1024, // 15MB
    modelsFiles: 50 * 1024 * 1024, // 50MB
    hideLevel: 50 * 1024 * 1024, // 50MB
    supportFiles: 50 * 1024 * 1024, // 50MB
    roomSnaps: 50 * 1024 * 1024, // 50MB
    presentationDrawings: 25 * 1024 * 1024, // 25MB
    constructionDrawingsPdf: 25 * 1024 * 1024, // 25MB
    constructionDrawingsDwg: 100 * 1024 * 1024 // 100MB
  };
  
  return sizeLimits[category] || 10 * 1024 * 1024; // Default 10MB
}
