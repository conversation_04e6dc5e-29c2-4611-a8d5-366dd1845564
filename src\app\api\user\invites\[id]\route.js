import { auth } from "@/auth";
import { NextResponse } from "next/server";
import { MongoClient } from "mongodb";

const uri = process.env.MONGODB_URI;

async function getDatabase() {
  const client = new MongoClient(uri);
  await client.connect();
  return client.db('luyarisite');
}

export async function DELETE(request, { params }) {
  try {
    const session = await auth();
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { id } = params;
    
    const db = await getDatabase();
    const invitesCollection = db.collection('invites');

    // Delete the invite (only if it belongs to the current user)
    const result = await invitesCollection.deleteOne({
      id: id,
      senderEmail: session.user.email
    });

    if (result.deletedCount === 0) {
      return NextResponse.json({ error: 'Invite not found or unauthorized' }, { status: 404 });
    }

    return NextResponse.json({ success: true, message: 'Invite deleted successfully' });

  } catch (error) {
    console.error('Error deleting invite:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
