import { NextResponse } from 'next/server'
import { auth } from '@/auth'
import UserService from '@/libs/userSchema'

// GET /api/admin/users - Get all users with pagination and search
export async function GET(request) {
  try {
    const session = await auth()
    
    // Check if user is authenticated and is admin
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page')) || 1
    const limit = parseInt(searchParams.get('limit')) || 10
    const search = searchParams.get('search') || ''
    const sortBy = searchParams.get('sortBy') || 'dateCreated'
    const sortOrder = searchParams.get('sortOrder') || 'desc'

    const result = await UserService.getAllUsers(page, limit, search, sortBy, sortOrder)
    
    return NextResponse.json(result)
  } catch (error) {
    console.error('Error in GET /api/admin/users:', error)
    return NextResponse.json(
      { error: 'Failed to fetch users' },
      { status: 500 }
    )
  }
}

// POST /api/admin/users - Create new user (if needed)
export async function POST(request) {
  try {
    const session = await auth()
    
    // Check if user is authenticated and is admin
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      )
    }

    const body = await request.json()
    
    // This endpoint could be used for manual user creation if needed
    // For now, users are created through authentication flow
    
    return NextResponse.json(
      { message: 'User creation through auth flow only' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Error in POST /api/admin/users:', error)
    return NextResponse.json(
      { error: 'Failed to create user' },
      { status: 500 }
    )
  }
}

// DELETE /api/admin/users - Bulk delete users
export async function DELETE(request) {
  try {
    const session = await auth()
    
    // Check if user is authenticated and is admin
    if (!session?.user || session.user.role !== 'admin') {
      return NextResponse.json(
        { error: 'Unauthorized. Admin access required.' },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { userIds } = body

    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json(
        { error: 'User IDs array is required' },
        { status: 400 }
      )
    }

    // Prevent admin from deleting themselves
    if (userIds.includes(session.user.id)) {
      return NextResponse.json(
        { error: 'Cannot delete your own account' },
        { status: 400 }
      )
    }

    const result = await UserService.deleteMultipleUsers(userIds)
    
    return NextResponse.json(result)
  } catch (error) {
    console.error('Error in DELETE /api/admin/users:', error)
    return NextResponse.json(
      { error: 'Failed to delete users' },
      { status: 500 }
    )
  }
}
