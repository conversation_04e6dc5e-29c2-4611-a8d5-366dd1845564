import { signIn } from '@/auth'
import { <PERSON>Mail, FiUser } from 'react-icons/fi'
import { FcGoogle } from 'react-icons/fc'
import Link from 'next/link'

export default async function SignInPage({ searchParams }) {
  const params = await searchParams
  const callbackUrl = params?.callbackUrl || '/'

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-neutral-50 to-neutral-100"></div>

      {/* Geometric Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-32 h-32 bg-neutral-200/30 rounded-full blur-xl"></div>
        <div className="absolute bottom-20 right-20 w-48 h-48 bg-neutral-300/20 rounded-full blur-2xl"></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-neutral-200/40 rounded-full blur-lg"></div>
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <div className="max-w-md w-full bg-white rounded-lg shadow-2xl p-8 border border-neutral-200">
          <div className="text-center mb-8">
            <Link href="/" className="text-2xl font-thin tracking-widest text-neutral-900 mb-6 block hover:text-neutral-700 transition-colors">
              luyari.
            </Link>
            <div className="h-px bg-neutral-300 w-12 mx-auto mb-6"></div>
            <h1 className="chaumet-heading text-2xl text-neutral-900 mb-2">
              Welcome Back
            </h1>
            <p className="text-neutral-600 font-light tracking-wide">
              Sign in to your account to continue
            </p>
          </div>

        <div className="space-y-4">
          {/* Google Sign In */}
          <form
            action={async () => {
              'use server'
              await signIn('google', { redirectTo: callbackUrl })
            }}
          >
            <button
              type="submit"
              className="chaumet-button border-neutral-300 text-neutral-700 group w-full"
            >
              <span className="relative z-10 group-hover:text-white transition-colors duration-500 flex items-center justify-center gap-3">
                <FcGoogle className="w-5 h-5" />
                Continue with Google
              </span>
            </button>
          </form>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="chaumet-divider" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-4 bg-white text-neutral-500 font-light tracking-wider uppercase">Or</span>
            </div>
          </div>

          {/* Email Magic Link */}
          <form
            action={async (formData) => {
              'use server'
              const email = formData.get('email')
              await signIn('nodemailer', {
                email,
                redirectTo: callbackUrl
              })
            }}
            className="space-y-4"
          >
            <div>
              <label htmlFor="email" className="block text-sm font-light text-neutral-600 mb-2 tracking-wider uppercase">
                Email address
              </label>
              <div className="relative">
                <FiMail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-neutral-400 h-4 w-4" />
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  placeholder="Enter your email"
                  className="chaumet-input pl-20"
                />
              </div>
            </div>

            <button
              type="submit"
              className="chaumet-button border-neutral-900 text-neutral-900 group w-full"
            >
              <span className="relative z-10 group-hover:text-white transition-colors duration-500">
                Send Magic Link
              </span>
            </button>
          </form>

        </div>

          <div className="mt-6 text-center">
            <p className="text-sm text-neutral-500 font-light">
              By signing in, you agree to our terms of service and privacy policy.
            </p>
          </div>

          {params?.error && (
            <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-700 font-light">
                {params.error === 'OAuthSignin' && 'Error occurred during OAuth sign in.'}
                {params.error === 'OAuthCallback' && 'Error occurred during OAuth callback.'}
                {params.error === 'OAuthCreateAccount' && 'Could not create OAuth account.'}
                {params.error === 'EmailCreateAccount' && 'Could not create email account.'}
                {params.error === 'Callback' && 'Error occurred during callback.'}
                {params.error === 'OAuthAccountNotLinked' && 'OAuth account not linked. Try signing in with a different method.'}
                {params.error === 'EmailSignin' && 'Check your email for the sign in link.'}
                {params.error === 'CredentialsSignin' && 'Sign in failed. Check your credentials.'}
                {params.error === 'SessionRequired' && 'Please sign in to access this page.'}
                {!['OAuthSignin', 'OAuthCallback', 'OAuthCreateAccount', 'EmailCreateAccount', 'Callback', 'OAuthAccountNotLinked', 'EmailSignin', 'CredentialsSignin', 'SessionRequired'].includes(params.error) && 'An error occurred during sign in.'}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
