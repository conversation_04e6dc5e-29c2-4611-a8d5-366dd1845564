'use client'
import React, { useEffect, useState } from 'react'
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import { DoubleSide, TextureLoader } from 'three'
import { Html } from '@react-three/drei'
import LoadingSpinner from '../LoadingSpinner'
import ExperienceSpiner from './ExperienceSpiner'

export default function Experience360({ data }) {
  const { experienceState } = useExperienceContext()
  const [textureUrls, setTextureUrls] = useState([])
  const [loadedTextures, setLoadedTextures] = useState([])
  const [loadingProgress, setLoadingProgress] = useState(0)
  const [isLoading, setIsLoading] = useState(false)

  // Initialize texture URLs from data
  useEffect(() => {
    if (data?._360sImages) {
      // Flatten the nested array structure
      const urls = data._360sImages.flat().map(item => item?.url).filter(Boolean)
      setTextureUrls(urls)
    }
  }, [data])

  useEffect(() => {
    const loadTextures = async () => {
      if (textureUrls.length === 0) return

      setIsLoading(true)
      setLoadingProgress(0)
      const textures = []

      try {
        // Load textures sequentially to avoid blocking
        for (let i = 0; i < textureUrls.length; i++) {
          const url = textureUrls[i]
          
          try {
            const loader = new TextureLoader()
            const texture = await new Promise((resolve, reject) => {
              loader.load(
                url,
                (tex) => {
                  // console.log(`Loaded texture ${i + 1}/${textureUrls.length}:`, url)
                  resolve(tex)
                },
                (progress) => {
                  // Update loading progress for current texture
                  const currentProgress = ((i + progress.loaded / progress.total) / textureUrls.length) * 100
                  setLoadingProgress(currentProgress)
                },
                (error) => {
                  console.error(`Failed to load texture from ${url}:`, error)
                  reject(error)
                }
              )
            })
            
            textures.push(texture)
            
            // Update loaded textures array after each successful load
            setLoadedTextures([...textures])
            
          } catch (error) {
            console.error(`Failed to load texture from ${url}:`, error)
            // Push null for failed textures to maintain index consistency
            textures.push(null)
          }
          
          // Update progress after each texture (success or failure)
          setLoadingProgress(((i + 1) / textureUrls.length) * 100)
        }
        
        // console.log('All textures loaded:', textures.length)
        
      } catch (error) {
        console.error('Error in texture loading process:', error)
      } finally {
        setIsLoading(false)
      }
    }

    if (textureUrls.length > 0) {
      loadTextures()
    }
  }, [textureUrls])

  // Get current texture based on experience state
  const currentTexture = loadedTextures[experienceState?.textureIndex] || loadedTextures[0]

  // console.log('Experience360:', {
  //   totalTextures: loadedTextures.length,
  //   currentIndex: experienceState?.textureIndex,
  //   currentTexture: currentTexture,
  //   loadingProgress: Math.round(loadingProgress),
  //   isLoading
  // })

  return (
    <>
      {/* Loading indicator */}
      {isLoading && (
        <Html center className='z-10'>
          <ExperienceSpiner loadingProgress={loadingProgress}/>
        </Html>
      )}

      {/* Main 360 sphere */}
      {currentTexture && (
        <mesh scale={[1, 1, -1]}>
          <sphereGeometry args={[32, 64, 64]} />
          <meshBasicMaterial map={currentTexture} side={DoubleSide}/>
        </mesh>
      )}

      {/* Debug info (remove in production)
      {process.env.NODE_ENV === 'development' && (
        <mesh position={[0, -30, 0]}>
          <planeGeometry args={[20, 5]} />
          <meshBasicMaterial color="black" opacity={0.7} transparent />
        </mesh>
      )} */}
    </>
  )
}