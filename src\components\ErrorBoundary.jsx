'use client';

import React from 'react';
import { motion } from 'framer-motion';
import Link from 'next/link';
import { HiHome, HiRefresh } from 'react-icons/hi';
import { HiExclamationTriangle } from "react-icons/hi2";

export default function ErrorBoundary({ error, reset }) {
  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-neutral-50 to-neutral-100"></div>
      
      {/* Geometric Background Elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-32 h-32 bg-neutral-200/30 rounded-full blur-xl"></div>
        <div className="absolute bottom-20 right-20 w-48 h-48 bg-neutral-300/20 rounded-full blur-2xl"></div>
        <div className="absolute top-1/2 left-1/4 w-24 h-24 bg-neutral-200/40 rounded-full blur-lg"></div>
      </div>

      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8 }}
          className="max-w-md w-full text-center"
        >
          {/* Logo */}
          <Link href="/" className="text-3xl font-thin tracking-widest text-neutral-900 mb-8 block hover:text-neutral-700 transition-colors">
            luyari.
          </Link>

          {/* Error Icon */}
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="w-24 h-24 mx-auto mb-8 bg-red-50 rounded-full flex items-center justify-center"
          >
            <HiExclamationTriangle className="w-12 h-12 text-red-500" />
          </motion.div>

          {/* Divider */}
          <motion.div
            initial={{ opacity: 0, width: 0 }}
            animate={{ opacity: 1, width: '3rem' }}
            transition={{ duration: 0.8, delay: 0.4 }}
            className="h-px bg-neutral-300 mx-auto mb-8"
          />

          {/* Error Message */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 0.6 }}
            className="mb-8"
          >
            <h1 className="chaumet-heading text-3xl text-neutral-900 mb-4">
              Something went wrong
            </h1>
            <p className="text-neutral-600 font-light leading-relaxed mb-6">
              We apologize for the inconvenience. An unexpected error has occurred while processing your request.
            </p>
            
            {/* Error Details (only in development) */}
            {process.env.NODE_ENV === 'development' && error && (
              <details className="text-left bg-neutral-50 p-4 rounded-lg border border-neutral-200 mb-6">
                <summary className="cursor-pointer text-sm font-medium text-neutral-700 mb-2">
                  Error Details
                </summary>
                <pre className="text-xs text-neutral-600 overflow-auto">
                  {error.message}
                </pre>
              </details>
            )}
          </motion.div>

          {/* Action Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.8 }}
            className="space-y-4"
          >
            <button
              onClick={reset}
              className="chaumet-button border-neutral-900 text-neutral-900 group w-full"
            >
              <span className="relative z-10 group-hover:text-white transition-colors duration-500 flex items-center justify-center">
                <HiRefresh className="w-4 h-4 mr-2" />
                Try Again
              </span>
            </button>

            <Link
              href="/"
              className="chaumet-button border-neutral-300 text-neutral-600 group w-full block"
            >
              <span className="relative z-10 group-hover:text-white transition-colors duration-500 flex items-center justify-center">
                <HiHome className="w-4 h-4 mr-2" />
                Return Home
              </span>
            </Link>
          </motion.div>

          {/* Footer */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.8, delay: 1.0 }}
            className="mt-12"
          >
            <div className="chaumet-divider mb-6" />
            <p className="text-sm text-neutral-500 font-light">
              If this problem persists, please contact our support team.
            </p>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
}

// Global Error Boundary Component
export function GlobalErrorBoundary({ error, reset }) {
  return (
    <html>
      <body>
        <ErrorBoundary error={error} reset={reset} />
      </body>
    </html>
  );
}
