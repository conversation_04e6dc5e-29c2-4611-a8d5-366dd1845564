// src/components/forms/SelectInput.jsx
// Reusable select dropdown component for building forms

"use client";

import { useState } from 'react';

/**
 * SelectInput Component
 * @param {Object} props
 * @param {string} props.name - Input name
 * @param {string} props.label - Input label
 * @param {string} props.value - Selected value
 * @param {Function} props.onChange - Change handler
 * @param {Array} props.options - Array of options {value, label}
 * @param {string} props.placeholder - Placeholder text
 * @param {boolean} props.required - Is required field
 * @param {string} props.error - Error message
 * @param {string} props.helpText - Help text
 * @param {boolean} props.disabled - Is disabled
 */
export default function SelectInput({
  name,
  label,
  value = '',
  onChange,
  options = [],
  placeholder = 'Select an option...',
  required = false,
  error = '',
  helpText = '',
  disabled = false,
  className = ''
}) {
  const [isFocused, setIsFocused] = useState(false);

  const handleChange = (e) => {
    if (onChange) {
      onChange(e.target.value, name);
    }
  };

  const selectClasses = `
    w-full px-4 py-3 border rounded-lg transition-all duration-200 appearance-none
    ${error 
      ? 'border-red-500 focus:border-red-500 focus:ring-red-200' 
      : isFocused 
        ? 'border-blue-500 focus:border-blue-500 focus:ring-blue-200' 
        : 'border-gray-300 hover:border-gray-400'
    }
    ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white cursor-pointer'}
    focus:outline-none focus:ring-2 focus:ring-opacity-50
    ${className}
  `.trim();

  const labelClasses = `
    block text-sm font-medium mb-2 transition-colors duration-200
    ${error ? 'text-red-700' : 'text-gray-700'}
    ${required ? "after:content-['*'] after:text-red-500 after:ml-1" : ''}
  `.trim();

  return (
    <div className="mb-4">
      {label && (
        <label htmlFor={name} className={labelClasses}>
          {label}
        </label>
      )}
      
      <div className="relative">
        <select
          id={name}
          name={name}
          value={value}
          onChange={handleChange}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          required={required}
          disabled={disabled}
          className={selectClasses}
        >
          {placeholder && (
            <option value="" disabled>
              {placeholder}
            </option>
          )}
          {options.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        
        {/* Custom dropdown arrow */}
        <div className="absolute inset-y-0 right-0 flex items-center px-2 pointer-events-none">
          <svg 
            className={`w-5 h-5 transition-colors duration-200 ${
              error ? 'text-red-500' : 'text-gray-400'
            }`} 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        </div>
      </div>

      {/* Help text */}
      {helpText && !error && (
        <p className="text-sm text-gray-600 mt-1">{helpText}</p>
      )}

      {/* Error message */}
      {error && (
        <p className="text-sm text-red-600 mt-1 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {error}
        </p>
      )}
    </div>
  );
}
