import React from 'react'
import { FiMail, FiCheckCircle } from 'react-icons/fi'

export default function VerifyRequestPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="mb-6">
          <div className="mx-auto w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-4">
            <FiMail className="w-8 h-8 text-blue-600" />
          </div>
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Check your email
          </h1>
          <p className="text-gray-600">
            We've sent you a magic link to sign in to your account.
          </p>
        </div>

        <div className="bg-blue-50 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-center mb-2">
            <FiCheckCircle className="w-5 h-5 text-blue-600 mr-2" />
            <span className="text-sm font-medium text-blue-800">
              Email sent successfully
            </span>
          </div>
          <p className="text-sm text-blue-700">
            Click the link in your email to complete the sign-in process.
          </p>
        </div>

        <div className="text-sm text-gray-500 space-y-2">
          <p>
            <strong>Didn't receive the email?</strong>
          </p>
          <ul className="text-left space-y-1">
            <li>• Check your spam or junk folder</li>
            <li>• Make sure you entered the correct email address</li>
            <li>• The link will expire in 24 hours</li>
          </ul>
        </div>

        <div className="mt-6 pt-6 border-t border-gray-200">
          <a
            href="/auth/signin"
            className="text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors"
          >
            ← Back to sign in
          </a>
        </div>
      </div>
    </div>
  )
}
