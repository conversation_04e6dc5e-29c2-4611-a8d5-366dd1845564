  'use client'
import React, { useState, useEffect } from 'react'

import Image from 'next/image'
import { usePathname } from 'next/navigation'
import { TbAugmentedReality2, TbHexagon3D } from 'react-icons/tb';// Import ACTIONS_EXPERIENCE
import { FaAngleLeft, FaAngleRight } from 'react-icons/fa6';
import { TbView360 } from "react-icons/tb";
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext';
import { ACTIONS_EXPERIENCE } from '@/libs/contextProviders/reducerExperience.jsx';

export default function 
ExperienceUi({data}) {
  const pathname=usePathname()
  const [toggleLevel,setToggleLevel]=useState(false)
  const [widenoptions,setWidenoptions]=useState(false)
  const [arCompatible, setArCompatible] = useState(null) // null = checking, true = supported, false = not supported

  useEffect(() => {
    // Check for WebXR support only on client side
    if (typeof window !== 'undefined' && typeof navigator !== 'undefined') {
      if (navigator.xr) {
        setArCompatible(true);
      } else {
        setArCompatible(false);
        console.error('WebXR not supported on this browser.');
      }
    } else {
      // Server-side or no navigator - assume not supported
      setArCompatible(false);
    }
  }, []);

  // Safe context usage with error handling
  let experienceState, experienceDispatch;
  try {
    const context = useExperienceContext();
    experienceState = context?.experienceState;
    experienceDispatch = context?.experienceDispatch;
  } catch (error) {
    console.error('ExperienceUi: Context error:', error);
    // Fallback state for production
    experienceState = {
      mode360: true,
      modeModel: false,
      modeAR: false,
      activeRoomSnap: null
    };
    experienceDispatch = () => console.warn('ExperienceUi: Dispatch not available');
  }

  // Ensure data has safe fallbacks
  const safeData = {
    _360sImages: data?._360sImages || [],
    hideLevel: data?.hideLevel || [],
    color: data?.color || [],
    roomSnaps: data?.roomSnaps || [],
    aRscale: [0.1, 0.35, 1],
    ...data
  };

  const cssIcon='flex cursor-pointer hover:scele-105 duration-300 ease-linear md:text-4xl text-3xl border-2- border-gray-50 rounded-full p-1 min-w-8 md:w-10 min-h-8 md:h-10'
  const cssTitle='flex md:text-xs text-xs text-center w-full justify-center font-medium'
  const btnStyle='flex bg-slate-800 text-xs capitalize hover:underline duration-300 ease-linear cursor-pointer items-center justify-center relative w-full h-10 text-wrap text-center'

  const modes=[
    {icon:<TbView360 className={cssIcon}/>, label: '360 View'},
    {icon:<TbHexagon3D className={cssIcon}/>, label: '3D Model'},
    {icon:<TbAugmentedReality2 className={cssIcon}/>, label: 'AR Mode'},
  ]

  const handleModes = (index) => {
    try {
      if (typeof index !== 'number' || !experienceDispatch) return;
      
      index === 0 && experienceDispatch({type: ACTIONS_EXPERIENCE.MODE_360})
      index === 1 && experienceDispatch({type: ACTIONS_EXPERIENCE.MODE_MODEL})
      index === 2 && experienceDispatch({type: ACTIONS_EXPERIENCE.MODE_AR})
    } catch (error) {
      console.error('ExperienceUi: Error in handleModes:', error);
    }
  }

  const handle360Index = (index) => {
    try {
      if (typeof index !== 'number' || !experienceDispatch) return;
      experienceDispatch({type: ACTIONS_EXPERIENCE.TEXTURE_INDEX, payload: index});
    } catch (error) {
      console.error('ExperienceUi: Error in handle360Index:', error);
    }
  }

  const handleARScaler = (index) => {
    try {
      if (typeof index !== 'number' || !experienceDispatch) return;
      experienceDispatch({type: ACTIONS_EXPERIENCE.AR_SCALER, payload: index});
    } catch (error) {
      console.error('ExperienceUi: Error in handle360Index:', error);
    }
  }

  const handleRoomSnapClick = (roomSnap) => {
    try {
      if (!roomSnap || !experienceDispatch) return;
      experienceDispatch({type: ACTIONS_EXPERIENCE.SET_ACTIVE_ROOM_SNAP, payload: roomSnap});
      experienceDispatch({type: ACTIONS_EXPERIENCE.SET_FIRST_PERSON_VIEW, payload: true});
    } catch (error) {
      console.error('ExperienceUi: Error in handleRoomSnapClick:', error);
    }
  }

  const handleLevelToHideClick = (levelToHide) => {
    try {
      if (!levelToHide || !experienceDispatch) return;
      const {priority, name} = levelToHide;
      setToggleLevel(!toggleLevel);
      experienceDispatch({type: ACTIONS_EXPERIENCE.LEVEL_TO_HIDE, payload: {priority, name}});
    } catch (error) {
      console.error('ExperienceUi: Error in handleLevelToHideClick:', error);
    }
  }

  // Render fallback if critical dependencies are missing, or normal UI
  return (
    <>
      {(!experienceState || !experienceDispatch) ? (
        <div className='flex z-50 max-w-fit absolute left-0 right-0 mx-auto top-5 text-white w-full gap-1 bg-red-500/50 items-center capitalize h-fit flex-col shadow rounded-xl p-2'>
          <span className='text-sm'>Experience UI Loading...</span>
        </div>
      ) : (
        <>
          {/* modes menu */}
          <div className='flex z-50 max-w-fit absolute left-0 right-0 mx-auto top-5 text-white w-full gap-1 bg-black/50 items-center capitalize h-fit flex-col shadow rounded-xl p-1' style={{pointerEvents: 'auto'}}>
            <div className='flex w-full h-fit p-1 bg-black/50 rounded-xl gap-1 overflow-x-auto overflow-hidden'>
          {arCompatible === true
            ? modes.map((mode, index) =>
              <button
                onClick={() => handleModes(index)}
                key={`mode-${index}`}
                className='flex text-sm items-center justify-center relative w-fit h-fit cursor-pointer bg-transparent border-none text-white rounded hover:bg-white/10 transition-colors'
                aria-label={`Switch to ${mode.label}`}
                type="button"
              >
                {mode?.icon}
              </button>
            )
            : arCompatible === false
              ? modes.slice(0,2).map((mode, index) =>
                <button
                  onClick={() => handleModes(index)}
                  key={`mode-${index}`}
                  className='flex text-sm items-center justify-center relative w-fit h-fit cursor-pointer bg-transparent border-none text-white rounded hover:bg-white/10 transition-colors'
                  aria-label={`Switch to ${mode.label}`}
                  type="button"
                >
                  {mode?.icon}
                </button>)
              : (
                  // Loading state while checking AR compatibility
                  <div className="text-sm text-gray-400 p-2">
                    Checking AR support...
                  </div>
                )
            }
        </div>
      </div>

      {/* ui title */}
      {experienceState?.activeRoomSnap?.length > 0 && (
        <div className='flex z-10 max-w-fit absolute right-4 mx-auto top-32 text-white w-full gap-1 bg-black/50 items-center capitalize h-fit flex-col shadow rounded-xl p-2'>
          <span className='text-sm'>{experienceState?.activeRoomSnap}</span>
        </div>
      )}

      {/* options menu */}
      <div className={`z-10 ${widenoptions ? 'md:w-1/6 w-28' : 'md:w-24 w-20'} h-full absolute my-auto text-white top-0 left-0 p-2 select-none duration-300 ease-linear`}>
        <div className='flex relative w-full h-full items-center'>
          <button 
            onClick={() => setWidenoptions(!widenoptions)} 
            className='flex z-20 absolute text-gray-500 w-fit h-fit -right-4 cursor-pointer rounded-full bg-gray-100 p-1 shadow border-none hover:bg-gray-200 transition-colors'
            aria-label={widenoptions ? 'Collapse options menu' : 'Expand options menu'}
            type="button"
          >
            {widenoptions ? <FaAngleLeft className='text-2xl'/> : <FaAngleRight className='text-2xl'/>}
          </button>
          <div className='flex w-full min-h-4 md:max-h-2/3 max-h-1/2 rounded-2xl shadow-md items-start bg-gray-200/35 gap-2 flex-col p-1 top-0 bottom-0 my-auto overflow-y-auto overflow-hidden'>
            <div className='flex w-full gap-1 bg-gray-200/50 h-fit flex-col rounded-xl'>
              {experienceState?.mode360 ? (
                <>
                  {/* The _360s Images menu */}
                  {safeData._360sImages?.length > 0 && (
                    <div className='flex w-full gap-1 bg-black/75 h-fit flex-col capitalize items-center shadow rounded-xl p-1'>
                      <span className={cssTitle}>360s images</span>
                      <div className='flex w-full h-fit p-1 bg-black/50 rounded-xl flex-wrap gap-1'>
                        {safeData._360sImages.map((i, index) =>
                          <button 
                            onClick={() => handle360Index(index)} 
                            key={i?.name || `image-${index}`} 
                            className='flex cursor-pointer bg-slate-800 items-center rounded-md overflow-hidden justify-center relative w-full min-h-10 md:min-h-12 border-none hover:bg-slate-700 transition-colors'
                            aria-label={`View 360 image ${i?.name || index + 1}`}
                            type="button"
                          >
                            <span className='absolute z-10 text-xs bottom-1 mx-auto text-white'>
                              {widenoptions ? (i?.name || `Image ${index + 1}`) : `${i?.name?.slice(0,3)}...`}
                            </span>
                            {i?.url && (
                              <Image
                                className='object-cover brightness-75'
                                src={i.url}
                                alt={`360 image ${index + 1}`}
                                fill
                                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                                priority={index === 0}
                              />
                            )}
                          </button>
                        )}
                      </div>
                    </div>
                  )}
                </>
              ) : (
                <>
                  {/* The hide levels menu */}
                  {safeData.hideLevel?.length > 0 && (
                    <div className='flex w-full gap-1 bg-black/50 h-fit flex-col capitalize items-center shadow rounded-xl p-1'>
                      <span className={cssTitle}>Levels toggle</span>
                      <div className='flex w-full h-fit p-1 bg-slate-900 rounded-xl flex-wrap gap-1'>
                        {safeData.hideLevel.map((i, index) =>
                          <button 
                            key={i?.name || `level-${index}`} 
                            className={`${btnStyle} border-none hover:bg-slate-700 transition-colors`} 
                            onClick={() => handleLevelToHideClick(i)}
                            aria-label={`Toggle level ${i?.name || index + 1}`}
                            type="button"
                          >
                            {widenoptions ? (i?.name?.split('.')[0] || `Level ${index + 1}`) : `${i?.name?.slice(0,3)}...`}
                          </button>
                        )}
                      </div>
                    </div>
                  )}

                  {/* The AR Scale menu */}
                  {safeData.aRscale?.length > 0 && experienceState?.modeAR && (
                    <div className='flex w-full gap-1 bg-black/50 h-fit flex-col capitalize items-center shadow rounded-xl p-1'>
                      <span className={cssTitle}>AR Scale toggles</span>
                      <div className='flex w-full h-fit p-1 bg-slate-900 rounded-xl flex-wrap gap-1'>
                        {safeData.aRscale.map((i, index) =>
                          <button 
                            key={i || `level-${index}`} 
                            className={`${btnStyle} border-none hover:bg-slate-700 transition-colors`} 
                            onClick={() => handleARScaler(i)}
                            aria-label={`Scale Model to ${i*100}%` || index}
                            type="button"
                          >
                            {`${i} scale` || index}
                          </button>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {/* The colors menu */}
                  {safeData.color?.length > 0 && (
                    <div className='flex w-full gap-1 bg-black/50 h-fit flex-col capitalize items-center shadow rounded-xl p-1'>
                      <span className={cssTitle}>Model colors</span>
                      <div className='flex w-full h-fit p-1 bg-slate-900 rounded-xl flex-wrap gap-1'>
                        {safeData.color.map((i, index) =>
                          <div key={i || `color-${index}`} className={btnStyle}>
                            {i || `Color ${index + 1}`}
                          </div>
                        )}
                      </div>
                    </div>
                  )}
                  
                  {/* The room snap menu */}
                  {safeData.roomSnaps?.length > 0 && (
                    <div className='flex w-full gap-1 bg-black/75 h-fit flex-col capitalize items-center shadow rounded-xl p-1'>
                      <span className={cssTitle}>Snap to view</span>
                      <div className='flex w-full h-fit p-1 bg-slate-900 rounded-xl flex-wrap gap-1'>
                        {safeData.roomSnaps.map((i, index) =>
                          <button 
                            key={i?.name || `snap-${index}`} 
                            className={`${btnStyle} border-none hover:bg-slate-700 transition-colors`} 
                            onClick={() => handleRoomSnapClick(i?.name)}
                            aria-label={`View ${i?.name?.split('.')[0] || `View ${index + 1}`}`}
                            type="button"
                          >
                            {widenoptions ? (i?.name?.split('.')[0] || `View ${index + 1}`) : `${i?.name?.slice(0,3)}...`}
                          </button>
                        )}
                      </div>
                    </div>
                  )}
                </>
              )}
            </div>
          </div>
        </div>
      </div>
        </>
      )}
    </>
  )
}
