import { IoIosSearch } from "react-icons/io";
import { MdFavorite } from "react-icons/md";
import { FaLocationDot } from "react-icons/fa6";
import { FaPhoneAlt } from "react-icons/fa";
import { MdOutlineEmail } from "react-icons/md";
import { FaFacebookF } from "react-icons/fa";
import { FaInstagram } from "react-icons/fa";
import { FaPinterest } from "react-icons/fa";
import { FaYoutube } from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";
import { IoHomeOutline, IoStatsChartOutline } from "react-icons/io5";
import { IoAnalytics } from "react-icons/io5";
import { AiOutlineShop, AiOutlineUser } from "react-icons/ai";
import { LiaDollarSignSolid } from "react-icons/lia";
import { IoMailOutline } from "react-icons/io5";
import { HiOutlineChartPie } from "react-icons/hi";
import { MdOutlineDynamicFeed } from "react-icons/md";
import { MdOutlineMessage } from "react-icons/md";
import { MdOutlineManageAccounts } from "react-icons/md";
import { TbReportSearch } from "react-icons/tb";
import { BsFilesAlt } from "react-icons/bs";

export const settings={
    url:process.env.NODE_ENV=='production' ? 'https://luyari.com' : 'https://localhost:3002',
    siteName:'luyari',
    siteMaxim:'experience design',
    auth:{
        login:[
            {id:0,type:'email',placeholder:'Email',name:'email'},
            {id:1,type:'password',placeholder:'password',name:'password'},
            {id:2,type:'submit',placeholder:'submit'},
        ],
        register:[
            {id:0,type:'text',placeholder:'name',name:'name'},
            {id:1,type:'text',placeholder:'surname',name:'surname'},
            {id:2,type:'text',placeholder:'username',name:'username'},
            {id:3,type:'number',placeholder:'number',name:'number'},
            {id:4,type:'email',placeholder:'email',name:'email'},
            {id:5,type:'password',placeholder:'password',name:'password'},
            {id:6,type:'password',placeholder:'re-enter password',name:'confirmPassword'},
            {id:7,type:'submit',placeholder:'submit'},
        ],
    },
    collections:[
        {name:'view all'},
        {name:'by size',listWrapList:[
                {
                    label:'plans by area',
                    list:[
                        'under 100m2',
                        '100m2 to 300m2',
                        '300m2 to 500m2',
                        '500m2 to 700m2',
                        'over 750m2',
                    ]
                },
                {
                    label:'plans by bedroomes',
                    list:[
                        '1 bedrooms',
                        '2 bedrooms',
                        '3 bedrooms',
                        '4+ bedrooms',
                    ]
                },
                {
                    label:'plans by floors',
                    list:[
                        '1 floor',
                        '2 floors',
                        '3+ floors',
                    ]
                }
            ]
        },
        {name:'by budget',listWrapList:[
                {
                    label:'plans by floors',
                    list:[
                        'under $100',
                        '$100 to $200',
                        '$200 to $300',
                        '$300 to $400',
                        '$400 to $700',
                        'over $700',
                    ]
                }
            ]
        },
        {name:'custom-plan'},
        {name:'by building type',listWrapList:[
            {
                label:'plans by building type',
                list:[
                    'single-storey',
                    'multi-residence',
                    'multi-storey',
                ]
            }
        ]},
    ],
    linkBackend:['dashboard','clientArea'],
    socials:[
        {id:0,icon:<FaFacebookF className="w-6 h-6" />},
        {id:1,icon:<FaInstagram className="w-6 h-6" />},
        {id:2,icon:<FaPinterest className="w-6 h-6" />},
        {id:3,icon:<FaYoutube className="w-6 h-6" />},
        {id:4,icon:<FaXTwitter className="w-6 h-6" />},
    ],
    dashboard:[
        {
            name:'dashboard',
            list:[
                {id:0,icon:<IoHomeOutline className="w-6 h-6" />,name:'home'},
                {id:1,icon:<IoAnalytics className="w-6 h-6" />,name:'analytics'},
                // {id:2,icon:<IoAnalytics className="w-6 h-6" />,name:'sales'},    
            ],
        },
        {
            name:'quick menu',
            list:[
                {id:2,icon:<AiOutlineUser className="w-6 h-6" />,name:'users'},
                {id:3,icon:<AiOutlineShop className="w-6 h-6" />,name:'products'},
                {id:3,icon:<BsFilesAlt className="w-6 h-6" />,name:'clientProjects'},
                {id:4,icon:<LiaDollarSignSolid className="w-6 h-6" />,name:'transactions'},
                {id:5,icon:<HiOutlineChartPie className="w-6 h-6" />,name:'reports'},
            ],
        },
        {
            name:'notifcations',
            list:[
                {id:6,icon:<IoMailOutline className="w-6 h-6" />,name:'mail'},
                {id:7,icon:<MdOutlineDynamicFeed className="w-6 h-6" />,name:'feedback'},
                {id:8,icon:<MdOutlineMessage className="w-6 h-6" />,name:'messages'},
            ],
        },
        {
            name:'admin',
            list:[
                {id:9,icon:<MdOutlineManageAccounts className="w-6 h-6" />,name:'manage'},
                {id:11,icon:<TbReportSearch className="w-6 h-6" />,name:'reports'},
            ],
        },
    ],
    dashboardClient:[
        // {
        //     name:'dashboard',
        //     list:[
        //         {id:0,icon:<IoHomeOutline className="w-6 h-6" />,name:'home'},   
        //     ],
        // },
        {
            name:'quick menu',
            list:[
                {id:4,icon:<IoStatsChartOutline className="w-6 h-6" />,name:'summary'},
                {id:5,icon:<HiOutlineChartPie className="w-6 h-6" />,name:'reports'},
            ],
        },
        {
            name:'notifcations',
            list:[
                // {id:6,icon:<IoMailOutline className="w-6 h-6" />,name:'mail'},
                // {id:7,icon:<MdOutlineDynamicFeed className="w-6 h-6" />,name:'feedback'},
                {id:8,icon:<MdOutlineMessage className="w-6 h-6" />,name:'messages'},
            ],
        },
        {
            name:'admin',
            list:[
                {id:9,icon:<MdOutlineManageAccounts className="w-6 h-6" />,name:'manage'},
                // {id:10,icon:<IoAnalytics className="w-6 h-6" />,name:'analytics'},
                {id:11,icon:<TbReportSearch className="w-6 h-6" />,name:'reports'},
            ],
        },
    ],
    auth:{
        login:[
            {id:0,type:'email',placeholder:'Email',name:'email'},
            {id:1,type:'password',placeholder:'password',name:'password'},
            {id:2,type:'submit',placeholder:'submit'},
        ],
        register:[
            {id:0,type:'text',placeholder:'name',name:'name'},
            {id:1,type:'text',placeholder:'surname',name:'surname'},
            {id:2,type:'text',placeholder:'username',name:'username'},
            {id:3,type:'number',placeholder:'number',name:'number'},
            {id:4,type:'email',placeholder:'email',name:'email'},
            {id:5,type:'password',placeholder:'password',name:'password'},
            {id:6,type:'password',placeholder:'re-enter password',name:'confirmPassword'},
            {id:7,type:'submit',placeholder:'submit'},
        ]
    },
    mockUserData:{
        id: 'user123',
        firstName: 'Jane',
        lastName: 'Doe',
        email: '<EMAIL>',
        bio: 'Passionate software developer and open-source enthusiast. Always learning and building!',
        location: 'New York, USA',
        profilePicture: 'https://via.placeholder.com/150/007bff/FFFFFF?text=JD', // Placeholder image
        joinedDate: '2023-01-15',
        socialLinks: {
            github: 'https://github.com/janedoe',
            linkedin: 'https://www.linkedin.com/in/janedoe/',
            twitter: 'https://twitter.com/janedoe',
        },
        recentActivity: [
            { id: 1, type: 'post', description: 'Published a new article on React Hooks.', date: '2025-06-01' },
            { id: 2, type: 'comment', description: 'Commented on a design system discussion.', date: '2025-05-28' },
            { id: 3, type: 'like', description: 'Liked a post about frontend performance.', date: '2025-05-25' },
        ],
    }
}