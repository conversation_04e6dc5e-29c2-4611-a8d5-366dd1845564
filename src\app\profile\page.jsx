'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import Link from 'next/link';
import {
  HiUser,
  HiMail,
  HiPhone,
  HiGlobeAlt,
  HiPlus,
  HiTrash,
  HiEye,
  HiCalendar,
  HiChartBar,
  HiArrowLeft,
  HiPencil,
  HiCheck,
  HiX
} from 'react-icons/hi';

export default function ProfilePage() {
  const { data: session, status, update } = useSession();
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [isEditing, setIsEditing] = useState(false);

  // Profile form state
  const [profileData, setProfileData] = useState({
    username: '',
    firstName: '',
    lastName: '',
    phone: '',
    email: '',
    website: ''
  });

  // Invites state
  const [invites, setInvites] = useState([]);
  const [newInviteEmail, setNewInviteEmail] = useState('');
  const [selectedProject, setSelectedProject] = useState('');
  const [userProjects, setUserProjects] = useState([]);
  const [userStats, setUserStats] = useState({
    totalInvites: 0,
    sentInvites: 0,
    viewedInvites: 0,
    pendingInvites: 0
  });

  // Redirect if not authenticated
  useEffect(() => {
    if (status === 'loading') return;
    if (!session?.user) {
      router.push('/auth/signin?callbackUrl=/profile');
      return;
    }
  }, [session, status, router]);

  // Load user data when component mounts
  useEffect(() => {
    if (session?.user) {
      setProfileData({
        username: session.user.username || '',
        firstName: session.user.firstName || '',
        lastName: session.user.lastName || '',
        phone: session.user.phone || '',
        email: session.user.email || '',
        website: session.user.website || ''
      });

      loadUserData();
    }
  }, [session]);

  const loadUserData = async () => {
    try {
      const response = await fetch('/api/user/profile');
      if (response.ok) {
        const data = await response.json();
        setUserProjects(data.projects || []);
        setInvites(data.invites || []);
        if (data.projects?.length > 0) {
          setSelectedProject(data.projects[0].id);
        }

        // Calculate stats
        const stats = {
          totalInvites: data.invites?.length || 0,
          sentInvites: data.invites?.filter(inv => inv.status === 'sent').length || 0,
          viewedInvites: data.invites?.filter(inv => inv.status === 'viewed').length || 0,
          pendingInvites: data.invites?.filter(inv => inv.status === 'pending').length || 0
        };
        setUserStats(stats);
      }
    } catch (error) {
      console.error('Error loading user data:', error);
    }
  };

  const handleProfileUpdate = async (e) => {
    e.preventDefault();
    setLoading(true);
    setMessage('');

    try {
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(profileData)
      });

      if (response.ok) {
        const updatedUser = await response.json();
        await update(updatedUser);
        setMessage('Profile updated successfully!');
        setIsEditing(false);
      } else {
        setMessage('Failed to update profile');
      }
    } catch (error) {
      setMessage('Error updating profile');
    } finally {
      setLoading(false);
    }
  };

  const handleAddInvite = async () => {
    if (!newInviteEmail || !selectedProject) return;

    setLoading(true);
    try {
      const response = await fetch('/api/user/invites', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          email: newInviteEmail,
          projectId: selectedProject
        })
      });

      if (response.ok) {
        const newInvite = await response.json();
        setInvites([...invites, newInvite]);
        setNewInviteEmail('');
        setMessage('Invite added successfully!');
        loadUserData(); // Refresh stats
      } else {
        setMessage('Failed to add invite');
      }
    } catch (error) {
      setMessage('Error adding invite');
    } finally {
      setLoading(false);
    }
  };

  const handleSendInvite = async (inviteId) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/user/invites/${inviteId}/send`, {
        method: 'POST'
      });

      if (response.ok) {
        setInvites(invites.map(invite =>
          invite.id === inviteId
            ? { ...invite, status: 'sent', sentAt: new Date().toISOString() }
            : invite
        ));
        setMessage('Invite sent successfully!');
        loadUserData(); // Refresh stats
      } else {
        setMessage('Failed to send invite');
      }
    } catch (error) {
      setMessage('Error sending invite');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteInvite = async (inviteId) => {
    setLoading(true);
    try {
      const response = await fetch(`/api/user/invites/${inviteId}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        setInvites(invites.filter(invite => invite.id !== inviteId));
        setMessage('Invite deleted successfully!');
        loadUserData(); // Refresh stats
      } else {
        setMessage('Failed to delete invite');
      }
    } catch (error) {
      setMessage('Error deleting invite');
    } finally {
      setLoading(false);
    }
  };

  const cancelEdit = () => {
    setIsEditing(false);
    setProfileData({
      username: session.user.username || '',
      firstName: session.user.firstName || '',
      lastName: session.user.lastName || '',
      phone: session.user.phone || '',
      email: session.user.email || '',
      website: session.user.website || ''
    });
  };

  if (status === 'loading') {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div className="w-8 h-8 border-2 border-neutral-300 border-t-neutral-900 rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-neutral-600 font-light">Loading profile...</p>
        </div>
      </div>
    );
  }

  if (!session?.user) {
    return null; // Will redirect in useEffect
  }

  return (
    <div className='relative top-20 flex w-full h-[calc(100%-80px)]'>
      <div className="min-h-screen bg-white w-full">
        {/* Header */}
        {/* <header className="bg-white border-b border-neutral-200 sticky top-0 z-10">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="flex justify-between items-center py-6">
              <div className="flex items-center space-x-4">
                <Link
                  href="/"
                  className="flex items-center text-neutral-600 hover:text-neutral-900 transition-colors"
                >
                  <HiArrowLeft className="w-5 h-5 mr-2" />
                  <span className="font-light">Back to Home</span>
                </Link>
                <div className="h-6 w-px bg-neutral-300"></div>
                <Link href="/" className="text-2xl font-thin tracking-widest text-neutral-900 hover:text-neutral-700 transition-colors">
                  luyari.
                </Link>
              </div>
              <div className="text-sm text-neutral-600 font-light">
                Profile Dashboard
              </div>
            </div>
          </div>
        </header> */}

        {/* Main Content */}
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          {/* Page Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            className="text-center mb-12"
          >
            <h1 className="chaumet-heading text-4xl text-neutral-900 mb-4">Account Settings</h1>
            <p className="text-neutral-600 font-light max-w-2xl mx-auto">
              Manage your profile information, project invitations, and account preferences.
            </p>
            <div className="chaumet-divider mt-8 mx-auto" style={{ width: '4rem' }} />
          </motion.div>

          {/* Message Display */}
          {message && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              className={`mb-6 p-4 rounded-lg text-sm max-w-4xl mx-auto ${
                message.includes('successfully')
                  ? 'bg-green-50 text-green-700 border border-green-200'
                  : 'bg-red-50 text-red-700 border border-red-200'
              }`}
            >
              {message}
            </motion.div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Profile Information */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.2 }}
              className="lg:col-span-2"
            >
              <div className="bg-white border border-neutral-200 rounded-lg p-8 shadow-sm">
                <div className="flex items-center justify-between mb-8">
                  <h2 className="chaumet-heading text-2xl text-neutral-900">Profile Information</h2>
                  {!isEditing ? (
                    <button
                      onClick={() => setIsEditing(true)}
                      className="flex items-center text-neutral-600 hover:text-neutral-900 transition-colors"
                    >
                      <HiPencil className="w-4 h-4 mr-2" />
                      <span className="font-light">Edit</span>
                    </button>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={cancelEdit}
                        className="flex items-center text-neutral-600 hover:text-neutral-900 transition-colors"
                      >
                        <HiX className="w-4 h-4 mr-1" />
                        <span className="font-light">Cancel</span>
                      </button>
                    </div>
                  )}
                </div>

                {/* Profile Picture and Basic Info */}
                <div className="flex items-center mb-8">
                  <div className="w-20 h-20 rounded-full bg-neutral-200 flex items-center justify-center mr-6">
                    {session.user.image ? (
                      <Image
                        src={session.user.image}
                        alt="Profile"
                        width={80}
                        height={80}
                        className="rounded-full"
                      />
                    ) : (
                      <span className="text-2xl font-light text-neutral-600">
                        {session.user.name?.charAt(0) || session.user.email?.charAt(0) || 'U'}
                      </span>
                    )}
                  </div>
                  <div>
                    <h3 className="text-xl font-light text-neutral-900 mb-1">
                      {profileData.firstName && profileData.lastName
                        ? `${profileData.firstName} ${profileData.lastName}`
                        : session.user.name || 'User'
                      }
                    </h3>
                    <p className="text-neutral-600 font-light mb-1">{session.user.email}</p>
                    <div className="flex items-center text-sm text-neutral-500">
                      <HiCalendar className="w-4 h-4 mr-1" />
                      <span>Member since {new Date(session.user.dateCreated || Date.now()).toLocaleDateString()}</span>
                    </div>
                  </div>
                </div>

                {/* Profile Form */}
                {isEditing ? (
                  <form onSubmit={handleProfileUpdate} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-light text-neutral-700 mb-2">
                          First Name
                        </label>
                        <input
                          type="text"
                          value={profileData.firstName}
                          onChange={(e) => setProfileData({...profileData, firstName: e.target.value})}
                          className="chaumet-input w-full"
                          placeholder="Enter your first name"
                        />
                      </div>
                      <div>
                        <label className="block text-sm font-light text-neutral-700 mb-2">
                          Last Name
                        </label>
                        <input
                          type="text"
                          value={profileData.lastName}
                          onChange={(e) => setProfileData({...profileData, lastName: e.target.value})}
                          className="chaumet-input w-full"
                          placeholder="Enter your last name"
                        />
                      </div>
                    </div>

                    <div>
                      <label className="block text-sm font-light text-neutral-700 mb-2">
                        Username
                      </label>
                      <input
                        type="text"
                        value={profileData.username}
                        onChange={(e) => setProfileData({...profileData, username: e.target.value})}
                        className="chaumet-input w-full"
                        placeholder="Enter your username"
                        required
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-light text-neutral-700 mb-2">
                        Phone Number
                      </label>
                      <input
                        type="tel"
                        value={profileData.phone}
                        onChange={(e) => setProfileData({...profileData, phone: e.target.value})}
                        className="chaumet-input w-full"
                        placeholder="Enter your phone number"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-light text-neutral-700 mb-2">
                        Website
                      </label>
                      <input
                        type="url"
                        value={profileData.website}
                        onChange={(e) => setProfileData({...profileData, website: e.target.value})}
                        className="chaumet-input w-full"
                        placeholder="https://your-website.com"
                      />
                    </div>

                    <div className="flex items-center space-x-4 pt-4">
                      <button
                        type="submit"
                        disabled={loading}
                        className="chaumet-button border-neutral-900 text-neutral-900 group"
                      >
                        <span className="relative z-10 group-hover:text-white transition-colors duration-500 flex items-center">
                          <HiCheck className="w-4 h-4 mr-2" />
                          {loading ? 'Saving...' : 'Save Changes'}
                        </span>
                      </button>
                    </div>
                  </form>
                ) : (
                  <div className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="flex items-center">
                        <HiUser className="w-5 h-5 text-neutral-400 mr-3" />
                        <div>
                          <p className="text-sm text-neutral-500 font-light">Username</p>
                          <p className="text-neutral-900 font-light">{profileData.username || 'Not set'}</p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <HiMail className="w-5 h-5 text-neutral-400 mr-3" />
                        <div>
                          <p className="text-sm text-neutral-500 font-light">Email</p>
                          <p className="text-neutral-900 font-light">{session.user.email}</p>
                        </div>
                      </div>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div className="flex items-center">
                        <HiPhone className="w-5 h-5 text-neutral-400 mr-3" />
                        <div>
                          <p className="text-sm text-neutral-500 font-light">Phone</p>
                          <p className="text-neutral-900 font-light">{profileData.phone || 'Not set'}</p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <HiGlobeAlt className="w-5 h-5 text-neutral-400 mr-3" />
                        <div>
                          <p className="text-sm text-neutral-500 font-light">Website</p>
                          <p className="text-neutral-900 font-light">{profileData.website || 'Not set'}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>

            {/* Sidebar - Stats and Invites */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.8, delay: 0.4 }}
              className="space-y-8"
            >
              {/* User Stats */}
              {session?.user?.role=='client' || 'admin' && (
                <div className="bg-white border border-neutral-200 rounded-lg p-6 shadow-sm">
                  <h3 className="chaumet-heading text-lg text-neutral-900 mb-6">Account Statistics</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <HiChartBar className="w-4 h-4 text-neutral-400 mr-2" />
                        <span className="text-sm text-neutral-600 font-light">Total Invites</span>
                      </div>
                      <span className="text-neutral-900 font-light">{userStats.totalInvites}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <HiMail className="w-4 h-4 text-green-500 mr-2" />
                        <span className="text-sm text-neutral-600 font-light">Sent</span>
                      </div>
                      <span className="text-neutral-900 font-light">{userStats.sentInvites}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <HiEye className="w-4 h-4 text-blue-500 mr-2" />
                        <span className="text-sm text-neutral-600 font-light">Viewed</span>
                      </div>
                      <span className="text-neutral-900 font-light">{userStats.viewedInvites}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <HiCalendar className="w-4 h-4 text-yellow-500 mr-2" />
                        <span className="text-sm text-neutral-600 font-light">Pending</span>
                      </div>
                      <span className="text-neutral-900 font-light">{userStats.pendingInvites}</span>
                    </div>
                  </div>
                </div>
              )}

              {/* Quick Actions */}
              {session?.user?.role=='client' || 'admin' && <div className="bg-white border border-neutral-200 rounded-lg p-6 shadow-sm">
                <h3 className="chaumet-heading text-lg text-neutral-900 mb-6">Quick Actions</h3>
                <div className="space-y-3">
                  <Link
                    href="/admin"
                    className="flex items-center text-neutral-600 hover:text-neutral-900 transition-colors"
                  >
                    <HiChartBar className="w-4 h-4 mr-2" />
                    <span className="font-light">Admin Dashboard</span>
                  </Link>
                  <Link
                    href="/"
                    className="flex items-center text-neutral-600 hover:text-neutral-900 transition-colors"
                  >
                    <HiGlobeAlt className="w-4 h-4 mr-2" />
                    <span className="font-light">View Portfolio</span>
                  </Link>
                </div>
              </div>}
            </motion.div>
          </div>

          {/* Invites Management Section */}
          {session?.user?.role=='client' || 'admin' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, delay: 0.6 }}
              className="mt-12"
            >
              <div className="bg-white border border-neutral-200 rounded-lg p-8 shadow-sm">
                <h2 className="chaumet-heading text-2xl text-neutral-900 mb-8">Project Invitations</h2>

                {/* Add New Invite */}
                <div className="mb-8 p-6 bg-neutral-50 rounded-lg">
                  <h3 className="text-lg font-light text-neutral-900 mb-4">Send New Invitation</h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <label className="block text-sm font-light text-neutral-700 mb-2">
                        Email Address
                      </label>
                      <input
                        type="email"
                        value={newInviteEmail}
                        onChange={(e) => setNewInviteEmail(e.target.value)}
                        className="chaumet-input w-full"
                        placeholder="Enter email address"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-light text-neutral-700 mb-2">
                        Select Project
                      </label>
                      <select
                        value={selectedProject}
                        onChange={(e) => setSelectedProject(e.target.value)}
                        className="chaumet-select w-full"
                      >
                        {userProjects.map((project) => (
                          <option key={project.id} value={project.id}>
                            {project.title}
                          </option>
                        ))}
                      </select>
                    </div>
                    <div className="flex items-end">
                      <button
                        onClick={handleAddInvite}
                        disabled={loading || !newInviteEmail || !selectedProject}
                        className="chaumet-button border-neutral-900 text-neutral-900 group w-full"
                      >
                        <span className="relative z-10 group-hover:text-white transition-colors duration-500 flex items-center justify-center">
                          <HiPlus className="w-4 h-4 mr-2" />
                          Add Invite
                        </span>
                      </button>
                    </div>
                  </div>
                </div>

                {/* Invites List */}
                <div>
                  <h3 className="text-lg font-light text-neutral-900 mb-4">Your Invitations</h3>
                  {invites.length === 0 ? (
                    <div className="text-center py-8 text-neutral-500 font-light">
                      No invitations yet. Add an email above to get started.
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {invites.map((invite) => (
                        <div key={invite.id} className="flex items-center justify-between p-4 border border-neutral-200 rounded-lg">
                          <div className="flex-1">
                            <div className="flex items-center space-x-4">
                              <div>
                                <p className="font-light text-neutral-900">{invite.recipientEmail}</p>
                                <p className="text-sm text-neutral-500">
                                  Project: {userProjects.find(p => p.id === invite.projectId)?.title || 'Unknown'}
                                </p>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-4">
                            <div className="text-right">
                              <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-light ${
                                invite.status === 'sent'
                                  ? 'bg-green-100 text-green-800'
                                  : invite.status === 'viewed'
                                  ? 'bg-blue-100 text-blue-800'
                                  : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {invite.status === 'sent' ? 'Sent' : invite.status === 'viewed' ? 'Viewed' : 'Pending'}
                              </span>
                              <p className="text-xs text-neutral-500 mt-1">
                                {invite.status === 'sent' && invite.sentAt
                                  ? `Sent ${new Date(invite.sentAt).toLocaleDateString()}`
                                  : invite.status === 'viewed' && invite.viewedAt
                                  ? `Viewed ${new Date(invite.viewedAt).toLocaleDateString()}`
                                  : `Created ${new Date(invite.createdAt).toLocaleDateString()}`
                                }
                              </p>
                            </div>
                            <div className="flex items-center space-x-2">
                              {invite.status === 'pending' && (
                                <button
                                  onClick={() => handleSendInvite(invite.id)}
                                  disabled={loading}
                                  className="text-green-600 hover:text-green-800 transition-colors"
                                  title="Send Invite"
                                >
                                  <HiMail className="w-4 h-4" />
                                </button>
                              )}
                              <button
                                onClick={() => handleDeleteInvite(invite.id)}
                                disabled={loading}
                                className="text-red-600 hover:text-red-800 transition-colors"
                                title="Delete Invite"
                              >
                                <HiTrash className="w-4 h-4" />
                              </button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </motion.div>
          )}
        </main>
      </div>
    </div>
  );
}
