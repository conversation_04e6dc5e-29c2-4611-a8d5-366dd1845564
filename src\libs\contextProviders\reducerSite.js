export const INITIAL_SITE_STATE={
    siteMobileHeightsTop:['h-[97%]','h-[55%]','h-[0%]'],
    siteMobileHeightsBottom:['h-[3%]','h-[45%]','h-[0%]'],
    heightIndex:0,
    scrollBtnShow:false,
    popupShow:false,
    enableAR:false,
    disenableAR:false,
    showCart:false,
    cartItemDetails:{},
}

export const ACTIONS_SITE={
    HEIGHT_ADJUST:'HEIGHT_ADJUST',
    TOGGLE_AR:'TOGGLE_AR',
    SHOW_HERO_SCROLL_BTN:'SHOW_HERO_SCROLL_BTN',
    SHOW_POPUP:'SHOW_POPUP',
    TOGGLE_CART:'TOGGLE_CART',
    ADD_TO_CART:'ADD_TO_CART',
}

export const reducerSiteContext=(state,action)=>{
    switch (action.type) {
        case 'HEIGHT_ADJUST':
            return{
                ...state,
                heightIndex:action.payload,
                // showWishlist:false
            }
        case 'TOGGLE_AR':
            return{
                ...state,
                enableAR:!state.enableAR,
                // showWishlist:false
            }
        case 'SHOW_HERO_SCROLL_BTN':
            return{
                ...state,
                scrollBtnShow:true,
                // showWishlist:false
            }
        case 'SHOW_POPUP':
            return{
                ...state,
                popupShow:!state.popupShow,
                // showWishlist:false
            }
        case 'TOGGLE_CART':
            return{
                ...state,
                showCart:!state.showCart,
                // showWishlist:false
            }
        case 'ADD_TO_CART':
            return{
                ...state,
                showCart:true,
                cartItemDetails:action.payload
                // showWishlist:false
            }
        case 'DISABLE_AR':
            return{
                ...state,
                disenableAR:!state.disenableAR,
                // showWishlist:false
            }
        default:
                state
            break;
    }
}