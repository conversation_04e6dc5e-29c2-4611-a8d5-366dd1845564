import React from 'react'

export default function ExperienceSpiner({loadingProgress, totalFile}) {
  return (
    <div className="flex flex-col items-center text-gray-500">
      <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-gray-500"></div>
      <div className="mt-4 text-center">
        {
          loadingProgress 
            ? <div className="text-sm opacity-75">
                {Math.round(loadingProgress)}% loaded
              </div> 
            : <div className="text-sm text-nowrap opacity-75">
              {totalFile} items loading...
            </div>
        }
      </div>
    </div>
  )
}
