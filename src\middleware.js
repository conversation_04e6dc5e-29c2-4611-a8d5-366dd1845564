import { NextResponse } from "next/server"

export function middleware(request) {
  const { pathname } = request.nextUrl

  // For admin routes, redirect to sign-in page
  // The actual authentication check will be done on the server side
  if (pathname.startsWith('/admin')) {
    // Let the page handle authentication check
    return NextResponse.next()
  }

  // For admin API routes, let them handle their own auth
  if (pathname.startsWith('/api/admin')) {
    return NextResponse.next()
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    '/admin/:path*',
    '/api/admin/:path*',
  ],
}
