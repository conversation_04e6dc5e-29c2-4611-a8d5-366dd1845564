import NextAuth from "next-auth"
import Google from "next-auth/providers/google"
import Nodemailer from "next-auth/providers/nodemailer"
import { MongoDBAdapter } from "@auth/mongodb-adapter"
import clientPromise from "@/libs/mongodb"

export const { handlers, signIn, signOut, auth } = NextAuth({
  adapter: MongoDBAdapter(clientPromise),
  providers: [
    Google({
      clientId: process.env.AUTH_GOOGLE_ID,
      clientSecret: process.env.AUTH_GOOGLE_SECRET,
    }),
    Nodemailer({
      server: {
        host: "smtp.hostinger.com",
        port: 465,
        auth: {
          user: "<EMAIL>",
          pass: process.env.EMAIL_PASSWORD,
        },
      },
      from: "<EMAIL>",
      sendVerificationRequest: async ({ identifier: email, url }) => {
        // Call our custom API route for sending emails
        try {
          await fetch(`${process.env.AUTH_URL}/api/send-email`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              to: email,
              subject: 'Sign in to <PERSON>yar<PERSON>',
              html: `
                <div style="max-width: 600px; margin: 0 auto; padding: 20px; font-family: Arial, sans-serif;">
                  <h2 style="color: #333; text-align: center;">Sign in to Luyari</h2>
                  <p style="color: #666; font-size: 16px;">Click the button below to sign in to your account:</p>
                  <div style="text-align: center; margin: 30px 0;">
                    <a href="${url}"
                       style="background-color: #2563eb; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block; font-weight: bold;">
                      Sign In
                    </a>
                  </div>
                  <p style="color: #666; font-size: 14px;">
                    If the button doesn't work, copy and paste this link into your browser:
                  </p>
                  <p style="color: #2563eb; word-break: break-all; font-size: 14px;">
                    ${url}
                  </p>
                  <p style="color: #999; font-size: 12px; margin-top: 30px;">
                    This link will expire in 24 hours. If you didn't request this email, you can safely ignore it.
                  </p>
                </div>
              `
            })
          })
        } catch (error) {
          console.error('Failed to send email:', error)
          throw new Error('Failed to send verification email')
        }
      }
    }),
  ],
  pages: {
    verifyRequest: "/auth/verify-request",
    signIn: "/auth/signin",
  },
  callbacks: {
    async signIn({ user }) {
      // Auto-assign admin role to specific email
      if (user.email === "<EMAIL>") {
        user.role = "admin"
      } else {
        user.role = "client"  // Default role is now "client"
      }
      return true
    },
    async session({ session, user }) {
      // Add user role and other fields to session
      if (user) {
        session.user.id = user.id
        session.user.role = user.role || "client"  // Default to "client" role
        session.user.phone = user.phone
        session.user.projects = user.projects || []
        session.user.dateCreated = user.dateCreated
      }
      return session
    },
    async jwt({ token, user }) {
      // Handle account linking for same email across providers
      if (user) {
        token.role = user.role
        token.phone = user.phone
        token.projects = user.projects
        token.dateCreated = user.dateCreated
      }
      return token
    },
  },
  events: {
    async createUser({ user }) {
      // Set default values for new users
      const client = await clientPromise
      const db = client.db()

      await db.collection("users").updateOne(
        { _id: user.id },
        {
          $set: {
            username: user.name || user.email?.split('@')[0] || 'User',
            phone: null,
            projects: [],
            role: user.email === "<EMAIL>" ? "admin" : "client",
            dateCreated: new Date(),
          }
        }
      )
    },
    async linkAccount({ user }) {
      // Handle account linking for users with same email
      const client = await clientPromise
      const db = client.db()

      // Update user role if it's the admin email
      if (user.email === "<EMAIL>") {
        await db.collection("users").updateOne(
          { _id: user.id },
          { $set: { role: "admin" } }
        )
      }
    },
  },
  session: {
    strategy: "database",
  },
})