// src/components/forms/ObjectInput.jsx
// Object input component for buildingSummary

"use client";

import { useState } from 'react';

/**
 * ObjectInput Component for buildingSummary
 * @param {Object} props
 * @param {string} props.name - Input name
 * @param {string} props.label - Input label
 * @param {Object} props.value - Object value
 * @param {Function} props.onChange - Change handler
 * @param {Array} props.fields - Array of field definitions
 * @param {boolean} props.required - Is required field
 * @param {Object} props.errors - Field-specific errors
 * @param {string} props.helpText - Help text
 * @param {boolean} props.disabled - Is disabled
 */
export default function ObjectInput({
  name,
  label,
  value = {},
  onChange,
  fields = [],
  required = false,
  errors = {},
  helpText = '',
  disabled = false,
  className = ''
}) {
  const [focusedField, setFocusedField] = useState(null);

  const handleFieldChange = (fieldValue, fieldName) => {
    const newValue = {
      ...value,
      [fieldName]: fieldValue
    };
    onChange(newValue, name);
  };

  const labelClasses = `
    block text-sm font-medium mb-3 transition-colors duration-200
    ${Object.keys(errors).length > 0 ? 'text-red-700' : 'text-gray-700'}
    ${required ? "after:content-['*'] after:text-red-500 after:ml-1" : ''}
  `.trim();

  return (
    <div className={`mb-6 ${className}`}>
      {label && (
        <label className={labelClasses}>
          {label}
        </label>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
        {fields.map((field) => {
          const fieldError = errors[field.name];
          const fieldValue = value[field.name] || '';
          
          const inputClasses = `
            w-full px-3 py-2 border rounded-md transition-all duration-200
            ${fieldError 
              ? 'border-red-500 focus:border-red-500 focus:ring-red-200' 
              : focusedField === field.name 
                ? 'border-blue-500 focus:border-blue-500 focus:ring-blue-200' 
                : 'border-gray-300 hover:border-gray-400'
            }
            ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
            focus:outline-none focus:ring-2 focus:ring-opacity-50
          `.trim();

          const fieldLabelClasses = `
            block text-xs font-medium mb-1 transition-colors duration-200
            ${fieldError ? 'text-red-700' : 'text-gray-600'}
            ${field.required ? "after:content-['*'] after:text-red-500 after:ml-1" : ''}
          `.trim();

          return (
            <div key={field.name} className="space-y-1">
              <label htmlFor={`${name}-${field.name}`} className={fieldLabelClasses}>
                {field.label}
              </label>
              
              <input
                id={`${name}-${field.name}`}
                name={`${name}-${field.name}`}
                type={field.type || 'number'}
                value={fieldValue}
                onChange={(e) => handleFieldChange(e.target.value, field.name)}
                onFocus={() => setFocusedField(field.name)}
                onBlur={() => setFocusedField(null)}
                placeholder={field.placeholder || ''}
                required={field.required}
                disabled={disabled}
                min={field.min || 0}
                max={field.max}
                step={field.step || 1}
                className={inputClasses}
              />
              
              {fieldError && (
                <p className="text-xs text-red-600 flex items-center">
                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                  </svg>
                  {fieldError}
                </p>
              )}
            </div>
          );
        })}
      </div>

      {/* Help text */}
      {helpText && Object.keys(errors).length === 0 && (
        <p className="text-sm text-gray-600 mt-2">{helpText}</p>
      )}

      {/* General error message */}
      {Object.keys(errors).length > 0 && (
        <p className="text-sm text-red-600 mt-2 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          Please fix the errors in the fields above
        </p>
      )}
    </div>
  );
}
