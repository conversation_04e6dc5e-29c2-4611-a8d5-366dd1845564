import PageWrapper from '@/components/PageWrapper'
import { settings } from '@/libs/siteSettings'
import Image from 'next/image'
import Link from 'next/link'
import React from 'react'

export default async function page() {
  const res=await fetch(`${settings.url}/api/buildings`)
  const {buildings} = await res.json()
  // console.log(buildings)
  return (
    <PageWrapper>
      <div className='flex w-full flex-col gap-5'>
        <h1 className='text-3xl font-bold'>Projects</h1>
        {/* <p className='text-xl font-bold'>Projects</p> */}
        <div className='flex w-full h-full flex-wrap overflow-y-auto scrollbar-hide'>
          {buildings.map(project => 
            <div
              key={project?._id}
              className='xl:w-1/3 md:w-1/2 w-full h-2/3 p-2 flex flex-col justify-center items-center'
            >
              <Link href={`/projects/${project?._id}`} className='relative w-full h-5/6 shadow rounded-lg overflow-hidden'>
                <Image src={project?.renders?.[0]?.url} alt='project'fill className='w-full brightness-90 h-full object-cover hover:scale-105 duration-300 ease-linear hover:brightness-105'/>
              </Link>
              <div className='w-full -bg-gray-100 rounded-lg mt-2 flex flex-col items-start'> 
                <h2 className='text-2xl font-bold'>{project?.buildingTitle}</h2>
                <p className='capitalize text-sm font-light'>{project?.buildingType}</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </PageWrapper>
  )
}
