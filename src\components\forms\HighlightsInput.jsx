// src/components/forms/HighlightsInput.jsx
// Complex array input for buildingHighlights with title and description

"use client";

import { useState } from 'react';

/**
 * HighlightsInput Component for buildingHighlights
 * @param {Object} props
 * @param {string} props.name - Input name
 * @param {string} props.label - Input label
 * @param {Array} props.value - Array of highlight objects
 * @param {Function} props.onChange - Change handler
 * @param {boolean} props.required - Is required field
 * @param {string} props.error - Error message
 * @param {string} props.helpText - Help text
 * @param {boolean} props.disabled - Is disabled
 * @param {number} props.maxItems - Maximum number of items
 */
export default function HighlightsInput({
  name,
  label,
  value = [],
  onChange,
  required = false,
  error = '',
  helpText = '',
  disabled = false,
  maxItems = 10,
  className = ''
}) {
  const [newHighlight, setNewHighlight] = useState({ title: '', description: '' });
  const [editingIndex, setEditingIndex] = useState(-1);
  const [validationErrors, setValidationErrors] = useState({});

  const validateHighlight = (highlight) => {
    const errors = {};
    
    if (!highlight.title.trim()) {
      errors.title = 'Title is required';
    }
    
    if (!highlight.description.trim()) {
      errors.description = 'Description is required';
    }
    
    // Check for duplicate titles (case-insensitive)
    const isDuplicateTitle = value.some((item, index) => 
      index !== editingIndex && 
      item.title.toLowerCase() === highlight.title.toLowerCase()
    );
    
    if (isDuplicateTitle) {
      errors.title = 'A highlight with this title already exists';
    }
    
    return errors;
  };

  const addHighlight = () => {
    const errors = validateHighlight(newHighlight);
    setValidationErrors(errors);
    
    if (Object.keys(errors).length > 0) {
      return;
    }
    
    if (value.length >= maxItems) {
      return;
    }
    
    const newValue = [...value, { ...newHighlight }];
    onChange(newValue, name);
    setNewHighlight({ title: '', description: '' });
    setValidationErrors({});
  };

  const updateHighlight = (index) => {
    const errors = validateHighlight(newHighlight);
    setValidationErrors(errors);
    
    if (Object.keys(errors).length > 0) {
      return;
    }
    
    const newValue = [...value];
    newValue[index] = { ...newHighlight };
    onChange(newValue, name);
    setEditingIndex(-1);
    setNewHighlight({ title: '', description: '' });
    setValidationErrors({});
  };

  const removeHighlight = (index) => {
    const newValue = value.filter((_, i) => i !== index);
    onChange(newValue, name);
    
    if (editingIndex === index) {
      setEditingIndex(-1);
      setNewHighlight({ title: '', description: '' });
      setValidationErrors({});
    }
  };

  const startEditing = (index) => {
    setEditingIndex(index);
    setNewHighlight({ ...value[index] });
    setValidationErrors({});
  };

  const cancelEditing = () => {
    setEditingIndex(-1);
    setNewHighlight({ title: '', description: '' });
    setValidationErrors({});
  };

  const handleInputChange = (field, inputValue) => {
    setNewHighlight(prev => ({
      ...prev,
      [field]: inputValue
    }));
    
    // Clear validation error for this field
    if (validationErrors[field]) {
      setValidationErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const labelClasses = `
    block text-sm font-medium mb-3 transition-colors duration-200
    ${error ? 'text-red-700' : 'text-gray-700'}
    ${required ? "after:content-['*'] after:text-red-500 after:ml-1" : ''}
  `.trim();

  return (
    <div className={`mb-6 ${className}`}>
      {label && (
        <label className={labelClasses}>
          {label}
        </label>
      )}
      
      {/* Existing highlights */}
      <div className="space-y-3 mb-4">
        {value.map((highlight, index) => (
          <div key={index} className="p-4 border border-gray-200 rounded-lg bg-gray-50">
            <div className="flex justify-between items-start mb-2">
              <h4 className="font-medium text-gray-900">{highlight.title}</h4>
              {!disabled && (
                <div className="flex space-x-2">
                  <button
                    type="button"
                    onClick={() => startEditing(index)}
                    className="text-blue-600 hover:text-blue-800 text-sm"
                  >
                    Edit
                  </button>
                  <button
                    type="button"
                    onClick={() => removeHighlight(index)}
                    className="text-red-600 hover:text-red-800 text-sm"
                  >
                    Remove
                  </button>
                </div>
              )}
            </div>
            <p className="text-gray-700 text-sm">{highlight.description}</p>
          </div>
        ))}
      </div>

      {/* Add/Edit form */}
      {!disabled && value.length < maxItems && (
        <div className="p-4 border border-gray-300 rounded-lg bg-white">
          <h4 className="font-medium text-gray-900 mb-3">
            {editingIndex >= 0 ? 'Edit Highlight' : 'Add New Highlight'}
          </h4>
          
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Title *
              </label>
              <input
                type="text"
                value={newHighlight.title}
                onChange={(e) => handleInputChange('title', e.target.value)}
                placeholder="Enter highlight title..."
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  validationErrors.title ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {validationErrors.title && (
                <p className="text-sm text-red-600 mt-1">{validationErrors.title}</p>
              )}
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Description *
              </label>
              <textarea
                value={newHighlight.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                placeholder="Enter highlight description..."
                rows={3}
                className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  validationErrors.description ? 'border-red-500' : 'border-gray-300'
                }`}
              />
              {validationErrors.description && (
                <p className="text-sm text-red-600 mt-1">{validationErrors.description}</p>
              )}
            </div>
            
            <div className="flex space-x-2">
              <button
                type="button"
                onClick={editingIndex >= 0 ? () => updateHighlight(editingIndex) : addHighlight}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {editingIndex >= 0 ? 'Update' : 'Add'} Highlight
              </button>
              
              {editingIndex >= 0 && (
                <button
                  type="button"
                  onClick={cancelEditing}
                  className="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-500"
                >
                  Cancel
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Item count */}
      {maxItems && (
        <div className="text-xs text-gray-500 mt-2 text-right">
          {value.length}/{maxItems} highlights
        </div>
      )}

      {/* Help text */}
      {helpText && !error && (
        <p className="text-sm text-gray-600 mt-2">{helpText}</p>
      )}

      {/* Error message */}
      {error && (
        <p className="text-sm text-red-600 mt-2 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {error}
        </p>
      )}
    </div>
  );
}
