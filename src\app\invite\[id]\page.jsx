import { MongoClient } from "mongodb";
import { notFound } from "next/navigation";
import Link from "next/link";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "react-icons/hi";

const uri = process.env.MONGODB_URI;

async function getDatabase() {
  const client = new MongoClient(uri);
  await client.connect();
  return client.db('luyarisite');
}

async function getInvite(inviteId) {
  try {
    const db = await getDatabase();
    const invitesCollection = db.collection('invites');
    
    const invite = await invitesCollection.findOne({ id: inviteId });
    
    if (invite) {
      // Mark as viewed
      await invitesCollection.updateOne(
        { id: inviteId },
        { 
          $set: { 
            status: 'viewed',
            viewedAt: new Date().toISOString()
          }
        }
      );
    }
    
    return invite;
  } catch (error) {
    console.error('Error fetching invite:', error);
    return null;
  }
}

export default async function InvitePage({ params }) {
  const { id } = params;
  const invite = await getInvite(id);

  if (!invite) {
    notFound();
  }

  // Mock project data - replace with actual project fetching
  const project = {
    id: invite.projectId,
    title: 'Modern Villa Visualization',
    description: 'A stunning architectural visualization showcasing contemporary design with clean lines and natural materials.',
    images: [
      '/api/placeholder/800/600',
      '/api/placeholder/800/600',
      '/api/placeholder/800/600'
    ],
    details: {
      type: 'Residential',
      area: '450 sqm',
      location: 'California, USA',
      year: '2024'
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="bg-white border-b border-neutral-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <Link href="/" className="text-2xl font-thin tracking-widest text-neutral-900 hover:text-neutral-700 transition-colors">
              luyari.
            </Link>
            <div className="flex items-center space-x-4 text-sm text-neutral-600">
              <div className="flex items-center">
                <HiUser className="w-4 h-4 mr-2" />
                <span className="font-light">Shared by {invite.senderName}</span>
              </div>
              <div className="flex items-center">
                <HiCalendar className="w-4 h-4 mr-2" />
                <span className="font-light">{new Date(invite.createdAt).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Project Header */}
        <div className="text-center mb-12">
          <div className="flex items-center justify-center mb-4">
            <HiEye className="w-6 h-6 text-neutral-400 mr-2" />
            <span className="text-sm text-neutral-500 font-light tracking-wider uppercase">Project Showcase</span>
          </div>
          <h1 className="chaumet-heading text-4xl text-neutral-900 mb-4">{project.title}</h1>
          <p className="text-neutral-600 font-light max-w-2xl mx-auto leading-relaxed">
            {project.description}
          </p>
          <div className="chaumet-divider mt-8 mx-auto" style={{ width: '4rem' }} />
        </div>

        {/* Project Details */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12">
          {Object.entries(project.details).map(([key, value]) => (
            <div key={key} className="text-center">
              <div className="text-sm text-neutral-500 font-light tracking-wider uppercase mb-2">
                {key}
              </div>
              <div className="text-neutral-900 font-light">
                {value}
              </div>
            </div>
          ))}
        </div>

        {/* Project Images */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12">
          {project.images.map((image, index) => (
            <div key={index} className="aspect-[4/3] bg-neutral-100 rounded-lg overflow-hidden">
              <img
                src={image}
                alt={`${project.title} - View ${index + 1}`}
                className="w-full h-full object-cover hover:scale-105 transition-transform duration-500"
              />
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center bg-neutral-50 p-12 rounded-lg">
          <h2 className="chaumet-heading text-2xl text-neutral-900 mb-4">
            Interested in Our Work?
          </h2>
          <p className="text-neutral-600 font-light mb-8 max-w-xl mx-auto">
            We specialize in creating photorealistic architectural visualizations that bring your vision to life.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/"
              className="chaumet-button border-neutral-900 text-neutral-900 group"
            >
              <span className="relative z-10 group-hover:text-white transition-colors duration-500">
                View Our Portfolio
              </span>
            </Link>
            <a
              href={`mailto:${invite.senderEmail}?subject=Inquiry about ${project.title}`}
              className="chaumet-button border-neutral-300 text-neutral-600 group"
            >
              <span className="relative z-10 group-hover:text-white transition-colors duration-500 flex items-center justify-center">
                <HiMail className="w-4 h-4 mr-2" />
                Contact Us
              </span>
            </a>
          </div>
        </div>
      </main>

      {/* Footer */}
      <footer className="bg-neutral-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <Link href="/" className="text-2xl font-thin tracking-widest mb-4 block hover:text-neutral-300 transition-colors">
            luyari.
          </Link>
          <p className="text-neutral-400 font-light">
            © {new Date().getFullYear()} Luyari Architectural Visualization. All rights reserved.
          </p>
        </div>
      </footer>
    </div>
  );
}
