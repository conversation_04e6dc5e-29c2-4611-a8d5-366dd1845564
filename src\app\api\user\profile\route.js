import { auth } from "@/auth";
import UserService from "@/libs/userSchema";
import { NextResponse } from "next/server";

export async function GET() {
  try {
    const session = await auth();
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await UserService.getUserByEmail(session.user.email);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    // Mock projects data - replace with actual project fetching logic
    const projects = [
      { id: 'proj1', title: 'Modern Villa Visualization', status: 'completed' },
      { id: 'proj2', title: 'Urban Apartment Complex', status: 'in-progress' },
      { id: 'proj3', title: 'Commercial Building Render', status: 'completed' }
    ];

    // Mock invites data - replace with actual invites fetching logic
    const invites = [
      { 
        id: 'inv1', 
        email: '<EMAIL>', 
        projectId: 'proj1', 
        status: 'pending',
        createdAt: new Date().toISOString()
      }
    ];

    return NextResponse.json({
      user: {
        username: user.username,
        firstName: user.firstName,
        lastName: user.lastName,
        phone: user.phone,
        email: user.email,
        website: user.website,
        image: user.image
      },
      projects,
      invites
    });

  } catch (error) {
    console.error('Error fetching user profile:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(request) {
  try {
    const session = await auth();
    
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { username, firstName, lastName, phone, website } = body;

    // Validate input
    if (!username || username.trim().length < 2) {
      return NextResponse.json({ error: 'Username must be at least 2 characters' }, { status: 400 });
    }

    // Update user profile
    const updatedUser = await UserService.updateUser(session.user.email, {
      username: username.trim(),
      firstName: firstName?.trim() || '',
      lastName: lastName?.trim() || '',
      phone: phone?.trim() || '',
      website: website?.trim() || ''
    });

    if (!updatedUser) {
      return NextResponse.json({ error: 'Failed to update user' }, { status: 500 });
    }

    return NextResponse.json({
      user: {
        username: updatedUser.username,
        firstName: updatedUser.firstName,
        lastName: updatedUser.lastName,
        phone: updatedUser.phone,
        email: updatedUser.email,
        website: updatedUser.website,
        image: updatedUser.image
      }
    });

  } catch (error) {
    console.error('Error updating user profile:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
