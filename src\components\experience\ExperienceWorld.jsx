'use client'
import { Canvas } from '@react-three/fiber'
import React, { Suspense } from 'react'
// import ExperienceControls from './ExperienceControls'
// import ExperienceModels from './ExperienceModels'
// import Experience360 from './Experience360';
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext'
import LoadingSpinner from '../LoadingSpinner'
import dynamic from 'next/dynamic';
import { createXRStore, XR } from '@react-three/xr'
import ExperienceAR from './ExperienceAR'
import { Environment } from '@react-three/drei'

const ExperienceControls=dynamic(() => import('./ExperienceControls'),{ssr:false})
const ExperienceModels=dynamic(() => import('./ExperienceModels'),{ssr:false})
const Experience360=dynamic(() => import('./Experience360'),{ssr:false})
const store=createXRStore()

export default function ExperienceWorld({data}) {
  const {experienceState}=useExperienceContext() // experienceDispatch is not used here
  return (
    <Canvas>
      <Suspense fallback={<LoadingSpinner/>}>
        <ExperienceControls data={data}/>
        <Environment preset="city"/>
        {experienceState?.mode360 && <Experience360 data={data}/>}
        {experienceState?.modeModel && <ExperienceModels data={data}/>}
        {experienceState?.modeAR && <ExperienceAR data={data} store={store}/>}
      </Suspense>
    </Canvas>
  )
}
