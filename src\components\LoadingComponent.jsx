import React from 'react'

const Spinner = ({ size = 'h-6 w-6', color = 'text-gray-50', borderWidth = 'border-2', className = '' }) => {
  return (
    <div className={`animate-spin rounded-full ${borderWidth} border-t-transparent ${color} ${size} ${className}`}></div>
  );
};

// function name(params) {
  
// }

export default function LoadingComponent() {
  return (
    <div className="flex absolute m-auto top-0 left-0 right-0 bottom-0 z-10 items-center justify-center h-fit w-fit">
      <Spinner size="h-10 w-10" color="text-gray-50" borderWidth="border-4" className="items-center justify-center" />
    </div>
  )
}
