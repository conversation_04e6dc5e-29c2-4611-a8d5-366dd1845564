import PageWrapper from '@/components/PageWrapper'
import UsersManagement from '@/components/admin/UsersManagement'
import { auth } from '@/auth'
import { redirect } from 'next/navigation'
import Link from 'next/link'
import { HiOfficeBuilding, HiU<PERSON>s, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'react-icons/hi'
import React from 'react'

export default async function AdminPage() {
  const session = await auth()

  // Check if user is authenticated and is admin
  if (!session?.user || session.user.role !== 'admin') {
    redirect('/auth/signin')
  }

  return (
    <PageWrapper>
      <div className='flex w-full flex-col gap-6 overflow-y-auto'>
        <div className='border-b border-neutral-200 pb-6'>
          <h1 className='chaumet-heading text-4xl text-neutral-900'>Admin Dashboard</h1>
          <p className='text-lg text-neutral-600 font-light mt-2'>Manage users, buildings, and system settings</p>
          <div className="chaumet-divider mt-4" style={{ width: '4rem' }} />
        </div>

        {/* Quick Actions */}
        <div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
          <div className='bg-white rounded-lg border border-neutral-200 p-6'>
            <div className='flex items-center justify-between mb-4'>
              <div className='flex items-center'>
                <HiUsers className='h-8 w-8 text-neutral-600 mr-3' />
                <div>
                  <h3 className='chaumet-heading text-xl text-neutral-900'>User Management</h3>
                  <p className='text-sm text-neutral-600 font-light'>Manage user accounts and permissions</p>
                </div>
              </div>
            </div>
            <div className='flex space-x-3'>
              <Link
                href='/admin#users'
                className='chaumet-button border-neutral-900 text-neutral-900 group flex-1'
              >
                <span className='relative z-10 group-hover:text-white transition-colors duration-500 flex items-center justify-center'>
                  <HiEye className='w-4 h-4 mr-2' />
                  View Users
                </span>
              </Link>
            </div>
          </div>

          <div className='bg-white rounded-lg border border-neutral-200 p-6'>
            <div className='flex items-center justify-between mb-4'>
              <div className='flex items-center'>
                <HiOfficeBuilding className='h-8 w-8 text-neutral-600 mr-3' />
                <div>
                  <h3 className='chaumet-heading text-xl text-neutral-900'>Building Management</h3>
                  <p className='text-sm text-neutral-600 font-light'>Manage building projects and files</p>
                </div>
              </div>
            </div>
            <div className='flex space-x-3'>
              <Link
                href='/admin/buildings'
                className='chaumet-button border-neutral-900 text-neutral-900 group flex-1'
              >
                <span className='relative z-10 group-hover:text-white transition-colors duration-500 flex items-center justify-center'>
                  <HiEye className='w-4 h-4 mr-2' />
                  View Buildings
                </span>
              </Link>
              <Link
                href='/admin/buildings/create'
                className='chaumet-button border-neutral-600 text-neutral-600 group'
              >
                <span className='relative z-10 group-hover:text-white transition-colors duration-500 flex items-center justify-center'>
                  <HiPlus className='w-4 h-4 mr-2' />
                  Create
                </span>
              </Link>
            </div>
          </div>
        </div>

        <div className='bg-white rounded-lg border border-neutral-200' id='users'>
          <div className='px-6 py-4 border-b border-neutral-200'>
            <h2 className='chaumet-heading text-xl text-neutral-900'>Users Management</h2>
            <p className='text-sm text-neutral-600 font-light mt-1'>View, edit, and manage user accounts</p>
          </div>
          <div className='p-6'>
            <UsersManagement />
          </div>
        </div>
      </div>
    </PageWrapper>
  )
}
