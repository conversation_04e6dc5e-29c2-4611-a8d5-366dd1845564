// src/components/forms/FileUpload.jsx
// File upload component with Firebase integration and offline fallback

"use client";

import { useState, useRef } from 'react';
import { uploadMultipleFiles, validateFileType, getFileSizeLimit } from '../../libs/firebase/storage';

/**
 * FileUpload Component
 * @param {Object} props
 * @param {string} props.name - Input name
 * @param {string} props.label - Input label
 * @param {Array} props.value - Array of file objects
 * @param {Function} props.onChange - Change handler
 * @param {string} props.category - File category for validation
 * @param {string} props.projectTitle - Project title for storage path
 * @param {boolean} props.required - Is required field
 * @param {string} props.error - Error message
 * @param {string} props.helpText - Help text
 * @param {boolean} props.disabled - Is disabled
 * @param {boolean} props.multiple - Allow multiple files
 * @param {boolean} props.showPriority - Show priority input (for hideLevel)
 */
export default function FileUpload({
  name,
  label,
  value = [],
  onChange,
  category,
  projectTitle,
  required = false,
  error = '',
  helpText = '',
  disabled = false,
  multiple = true,
  showPriority = false,
  className = ''
}) {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState({});
  const [dragOver, setDragOver] = useState(false);
  const fileInputRef = useRef(null);

  const isProjectTitleRequired = !projectTitle || projectTitle.trim() === '';

  const handleFileSelect = async (files) => {
    if (isProjectTitleRequired) {
      alert('Please enter a project title before uploading files.');
      return;
    }

    if (!files || files.length === 0) return;

    setUploading(true);
    const fileArray = Array.from(files);
    const validFiles = [];
    const errors = [];

    // Validate files
    for (const file of fileArray) {
      if (!validateFileType(file, category)) {
        errors.push(`${file.name}: Invalid file type for ${category}`);
        continue;
      }

      const sizeLimit = getFileSizeLimit(category);
      if (file.size > sizeLimit) {
        errors.push(`${file.name}: File size exceeds limit (${Math.round(sizeLimit / 1024 / 1024)}MB)`);
        continue;
      }

      validFiles.push(file);
    }

    if (errors.length > 0) {
      alert('File validation errors:\n' + errors.join('\n'));
    }

    if (validFiles.length === 0) {
      setUploading(false);
      return;
    }

    try {
      // Get priorities for hideLevel files
      const priorities = showPriority ? 
        validFiles.map((_, index) => {
          const priority = prompt(`Enter priority for ${validFiles[index].name} (1-10):`);
          return priority ? parseInt(priority) : 1;
        }) : [];

      // Upload files
      const uploadResults = await uploadMultipleFiles(validFiles, projectTitle, category, priorities);
      
      // Filter successful uploads
      const successfulUploads = uploadResults.filter(result => result.success);
      const failedUploads = uploadResults.filter(result => !result.success);

      if (failedUploads.length > 0) {
        console.error('Some uploads failed:', failedUploads);
      }

      // Update value with new files
      const newValue = [...value, ...successfulUploads];
      onChange(newValue, name);

    } catch (error) {
      console.error('Upload error:', error);
      alert('Upload failed: ' + error.message);
    } finally {
      setUploading(false);
      setUploadProgress({});
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setDragOver(false);
    const files = e.dataTransfer.files;
    handleFileSelect(files);
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setDragOver(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setDragOver(false);
  };

  const removeFile = (index) => {
    const newValue = value.filter((_, i) => i !== index);
    onChange(newValue, name);
  };

  const getFileTypeIcon = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase();
    
    switch (extension) {
      case 'jpg':
      case 'jpeg':
      case 'png':
      case 'webp':
        return '🖼️';
      case 'pdf':
        return '📄';
      case 'glb':
        return '🎯';
      case 'dwg':
        return '📐';
      default:
        return '📁';
    }
  };

  const labelClasses = `
    block text-sm font-medium mb-2 transition-colors duration-200
    ${error ? 'text-red-700' : 'text-gray-700'}
    ${required ? "after:content-['*'] after:text-red-500 after:ml-1" : ''}
  `.trim();

  const dropzoneClasses = `
    border-2 border-dashed rounded-lg p-6 text-center transition-all duration-200 cursor-pointer
    ${isProjectTitleRequired 
      ? 'border-gray-200 bg-gray-50 cursor-not-allowed' 
      : dragOver 
        ? 'border-blue-500 bg-blue-50' 
        : error 
          ? 'border-red-300 bg-red-50 hover:border-red-400' 
          : 'border-gray-300 bg-gray-50 hover:border-gray-400'
    }
    ${uploading ? 'pointer-events-none opacity-50' : ''}
  `.trim();

  return (
    <div className={`mb-6 ${className}`}>
      {label && (
        <label className={labelClasses}>
          {label}
        </label>
      )}

      {/* Project title warning */}
      {isProjectTitleRequired && (
        <div className="mb-3 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
          <p className="text-sm text-yellow-800">
            ⚠️ Please enter a project title before uploading files.
          </p>
        </div>
      )}

      {/* File drop zone */}
      <div
        className={dropzoneClasses}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => !isProjectTitleRequired && !disabled && fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          multiple={multiple}
          onChange={(e) => handleFileSelect(e.target.files)}
          className="hidden"
          disabled={disabled || isProjectTitleRequired}
          accept={getAcceptedFileTypes(category)}
        />

        {uploading ? (
          <div className="space-y-2">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="text-sm text-gray-600">Uploading files...</p>
          </div>
        ) : (
          <div className="space-y-2">
            <div className="text-4xl">📁</div>
            <p className="text-sm text-gray-600">
              {isProjectTitleRequired 
                ? 'Enter project title to enable file upload'
                : 'Drag and drop files here, or click to select'
              }
            </p>
            <p className="text-xs text-gray-500">
              Supported: {getSupportedFileTypes(category)}
            </p>
          </div>
        )}
      </div>

      {/* Uploaded files list */}
      {value.length > 0 && (
        <div className="mt-4 space-y-2">
          <h4 className="text-sm font-medium text-gray-700">Uploaded Files:</h4>
          {value.map((file, index) => (
            <div key={index} className="flex items-center justify-between p-3 bg-white border border-gray-200 rounded-md">
              <div className="flex items-center space-x-3">
                <span className="text-lg">{getFileTypeIcon(file.name)}</span>
                <div>
                  <p className="text-sm font-medium text-gray-900">{file.name}</p>
                  <p className="text-xs text-gray-500">
                    {file.size ? `${Math.round(file.size / 1024)} KB` : ''} 
                    {file.priority ? ` • Priority: ${file.priority}` : ''}
                    {file.uploadMethod ? ` • ${file.uploadMethod}` : ''}
                  </p>
                </div>
              </div>
              {!disabled && (
                <button
                  type="button"
                  onClick={() => removeFile(index)}
                  className="text-red-600 hover:text-red-800 p-1"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Help text */}
      {helpText && !error && (
        <p className="text-sm text-gray-600 mt-2">{helpText}</p>
      )}

      {/* Error message */}
      {error && (
        <p className="text-sm text-red-600 mt-2 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {error}
        </p>
      )}
    </div>
  );
}

// Helper functions
function getAcceptedFileTypes(category) {
  const typeMap = {
    renders: '.jpg,.jpeg,.png,.webp',
    drawings: '.jpg,.jpeg,.png,.webp',
    _360sImages: '.jpg,.jpeg,.png,.webp',
    modelsFiles: '.glb',
    hideLevel: '.glb',
    supportFiles: '.glb',
    roomSnaps: '.glb',
    presentationDrawings: '.pdf',
    constructionDrawingsPdf: '.pdf',
    constructionDrawingsDwg: '.dwg'
  };
  return typeMap[category] || '*';
}

function getSupportedFileTypes(category) {
  const typeMap = {
    renders: 'JPG, PNG, WebP',
    drawings: 'JPG, PNG, WebP',
    _360sImages: 'JPG, PNG, WebP',
    modelsFiles: 'GLB',
    hideLevel: 'GLB',
    supportFiles: 'GLB',
    roomSnaps: 'GLB',
    presentationDrawings: 'PDF',
    constructionDrawingsPdf: 'PDF',
    constructionDrawingsDwg: 'DWG'
  };
  return typeMap[category] || 'All files';
}
