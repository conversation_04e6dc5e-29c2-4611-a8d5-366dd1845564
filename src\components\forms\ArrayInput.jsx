// src/components/forms/ArrayInput.jsx
// Array input component with tag-style UI and duplicate prevention

"use client";

import { useState } from 'react';

/**
 * ArrayInput Component for tags, colors, collections
 * @param {Object} props
 * @param {string} props.name - Input name
 * @param {string} props.label - Input label
 * @param {Array} props.value - Array of values
 * @param {Function} props.onChange - Change handler
 * @param {string} props.placeholder - Placeholder text
 * @param {boolean} props.required - Is required field
 * @param {string} props.error - Error message
 * @param {string} props.helpText - Help text
 * @param {boolean} props.disabled - Is disabled
 * @param {number} props.maxItems - Maximum number of items
 */
export default function ArrayInput({
  name,
  label,
  value = [],
  onChange,
  placeholder = 'Type and press Enter to add...',
  required = false,
  error = '',
  helpText = '',
  disabled = false,
  maxItems = 20,
  className = ''
}) {
  const [inputValue, setInputValue] = useState('');
  const [isFocused, setIsFocused] = useState(false);

  const handleInputChange = (e) => {
    setInputValue(e.target.value);
  };

  const handleKeyDown = (e) => {
    if (e.key === 'Enter' || e.key === ',') {
      e.preventDefault();
      addItem();
    } else if (e.key === 'Backspace' && inputValue === '' && value.length > 0) {
      // Remove last item if input is empty and backspace is pressed
      removeItem(value.length - 1);
    }
  };

  const addItem = () => {
    const trimmedValue = inputValue.trim();
    
    if (trimmedValue === '') return;
    
    // Check for duplicates (case-insensitive)
    const isDuplicate = value.some(item => 
      item.toLowerCase() === trimmedValue.toLowerCase()
    );
    
    if (isDuplicate) {
      setInputValue('');
      return;
    }
    
    // Check max items limit
    if (value.length >= maxItems) {
      setInputValue('');
      return;
    }
    
    const newValue = [...value, trimmedValue];
    onChange(newValue, name);
    setInputValue('');
  };

  const removeItem = (index) => {
    const newValue = value.filter((_, i) => i !== index);
    onChange(newValue, name);
  };

  const inputClasses = `
    flex-1 px-3 py-2 border-0 outline-none bg-transparent
    ${disabled ? 'cursor-not-allowed' : ''}
  `.trim();

  const containerClasses = `
    w-full min-h-[48px] px-3 py-2 border rounded-lg transition-all duration-200
    flex flex-wrap items-center gap-2
    ${error 
      ? 'border-red-500 focus-within:border-red-500 focus-within:ring-red-200' 
      : isFocused 
        ? 'border-blue-500 focus-within:border-blue-500 focus-within:ring-blue-200' 
        : 'border-gray-300 hover:border-gray-400'
    }
    ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}
    focus-within:outline-none focus-within:ring-2 focus-within:ring-opacity-50
    ${className}
  `.trim();

  const labelClasses = `
    block text-sm font-medium mb-2 transition-colors duration-200
    ${error ? 'text-red-700' : 'text-gray-700'}
    ${required ? "after:content-['*'] after:text-red-500 after:ml-1" : ''}
  `.trim();

  return (
    <div className="mb-4">
      {label && (
        <label className={labelClasses}>
          {label}
        </label>
      )}
      
      <div 
        className={containerClasses}
        onClick={() => !disabled && document.getElementById(`${name}-input`)?.focus()}
      >
        {/* Render existing tags */}
        {value.map((item, index) => (
          <span
            key={index}
            className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 border border-blue-200"
          >
            {item}
            {!disabled && (
              <button
                type="button"
                onClick={(e) => {
                  e.stopPropagation();
                  removeItem(index);
                }}
                className="ml-2 text-blue-600 hover:text-blue-800 focus:outline-none"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            )}
          </span>
        ))}
        
        {/* Input field */}
        {!disabled && value.length < maxItems && (
          <input
            id={`${name}-input`}
            type="text"
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            placeholder={value.length === 0 ? placeholder : ''}
            className={inputClasses}
            disabled={disabled}
          />
        )}
      </div>

      {/* Item count and limit */}
      {maxItems && (
        <div className="text-xs text-gray-500 mt-1 text-right">
          {value.length}/{maxItems} items
        </div>
      )}

      {/* Help text */}
      {helpText && !error && (
        <p className="text-sm text-gray-600 mt-1">{helpText}</p>
      )}

      {/* Error message */}
      {error && (
        <p className="text-sm text-red-600 mt-1 flex items-center">
          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
          </svg>
          {error}
        </p>
      )}
    </div>
  );
}
