'use client'
import React, { createContext, useContext, useReducer, useState } from 'react'
import { INITIAL_SITE_EXP_STATE, reducerSiteExperience } from './reducerSiteExperience'

export const SiteExperienceContext=createContext()

export default function SiteExperienceContextProvider({children}) {
    const [scrollIndex,setScrollIndex]=useState(0)
    const [checkingARSupport,setCheckingARSupport]=useState(false)
    const [siteExperienceState,siteExperienceDispatch]=useReducer(reducerSiteExperience,INITIAL_SITE_EXP_STATE)
  return (
    <SiteExperienceContext.Provider
      value={{siteExperienceState,siteExperienceDispatch,scrollIndex,setScrollIndex,checkingARSupport,setCheckingARSupport}}
    >
      {children}
    </SiteExperienceContext.Provider>
  )
}

export const  useSiteExperienceContext=()=>{
    return useContext(SiteExperienceContext)
}

