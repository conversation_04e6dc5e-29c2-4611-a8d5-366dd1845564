# Quick Setup Guide - Admin Dashboard

## Prerequisites
- Node.js 18+ installed
- MongoDB database access
- Google OAuth credentials
- SMTP email server access

## Installation Steps

### 1. Install Dependencies
```bash
npm install nodemailer
```
(Other dependencies already installed: next-auth, @auth/mongodb-adapter, mongodb)

### 2. Environment Configuration
Ensure your `.env.local` file contains:

```env
# MongoDB
MONGODB_URI="mongodb+srv://username:<EMAIL>/database"

# NextAuth.js
AUTH_SECRET="your-secret-key-here"
AUTH_URL="https://localhost:3002"

# Google OAuth
AUTH_GOOGLE_ID="your-google-client-id"
AUTH_GOOGLE_SECRET="your-google-client-secret"

# Email Provider
EMAIL_SERVER={"host":"smtp.hostinger.com","port":465,"auth":{"user":"<EMAIL>","pass":"your-email-password"}}
EMAIL_FROM="<EMAIL>"
```

### 3. Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URIs:
   - `https://localhost:3002/api/auth/callback/google`
   - `https://yourdomain.com/api/auth/callback/google`

### 4. Database Setup
The application will automatically create the necessary collections in MongoDB:
- `users` - User accounts
- `accounts` - OAuth account linking
- `sessions` - User sessions
- `verification_tokens` - Magic link tokens

### 5. Admin User Setup
The admin role is automatically assigned to `<EMAIL>` when this user signs in for the first time.

## Testing the Implementation

### 1. Start Development Server
```bash
npm run dev
```

### 2. Test Authentication
1. Navigate to `/auth/signin`
2. Try Google OAuth login
3. Try magic link email login
4. Verify redirect to verification page for magic links

### 3. Test Admin Dashboard
1. Sign in as `<EMAIL>`
2. Navigate to `/admin`
3. Verify user management interface loads
4. Test search, sort, and pagination
5. Test user editing and deletion

### 4. Test Security
1. Try accessing `/admin` without authentication (should redirect)
2. Try accessing `/admin` as regular user (should redirect)
3. Try accessing `/api/admin/users` without admin role (should return 401)

## Common Issues & Solutions

### Issue: "Invalid/Missing environment variable: MONGODB_URI"
**Solution**: Verify MONGODB_URI is correctly set in `.env.local`

### Issue: Google OAuth not working
**Solution**: 
- Check Google OAuth credentials
- Verify redirect URIs in Google Console
- Ensure AUTH_GOOGLE_ID and AUTH_GOOGLE_SECRET are correct

### Issue: Magic link emails not sending
**Solution**:
- Verify EMAIL_SERVER configuration
- Check SMTP credentials
- Test email server connectivity

### Issue: Admin dashboard not accessible
**Solution**:
- Ensure user has admin role in database
- Check middleware configuration
- Verify session is active

### Issue: Users not displaying in admin dashboard
**Solution**:
- Check MongoDB connection
- Verify users collection exists
- Check browser console for API errors

## File Locations

### Key Files Modified/Created:
- `src/auth.js` - NextAuth.js configuration
- `src/middleware.js` - Route protection
- `src/app/admin/page.jsx` - Admin dashboard
- `src/components/admin/UsersManagement.jsx` - User management UI
- `src/libs/userSchema.js` - Database operations
- `src/app/api/admin/users/` - API endpoints

### Pages Created:
- `/admin` - Admin dashboard
- `/auth/signin` - Sign-in page
- `/auth/verify-request` - Magic link verification

## Next Steps

1. **Test thoroughly** in development environment
2. **Deploy to staging** for further testing
3. **Configure production** environment variables
4. **Set up monitoring** for authentication flows
5. **Train admin users** on dashboard functionality

## Support

For issues or questions:
1. Check the main implementation guide: `docs/admin-dashboard-implementation.md`
2. Review NextAuth.js documentation: https://authjs.dev
3. Check MongoDB connection and permissions
4. Verify all environment variables are correctly set

## Production Deployment Checklist

- [ ] Update AUTH_URL to production domain
- [ ] Configure production MongoDB instance
- [ ] Set up production SMTP server
- [ ] Update Google OAuth redirect URIs for production
- [ ] Generate new AUTH_SECRET for production
- [ ] Test all authentication flows in production
- [ ] Verify admin dashboard functionality
- [ ] Set up monitoring and logging
