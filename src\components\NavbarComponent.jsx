'use client';

import React, { useState, useEffect } from 'react';
import UserSettings from './UserSettings';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { HiOutlineMenuAlt3, HiX } from 'react-icons/hi';
import SearchComponent from './SearchComponent';
import MenuComponent from './MenuComponent';
import { usePathname } from 'next/navigation';
import { settings } from '@/libs/siteSettings';
import { useSession } from 'next-auth/react';

export default function NavbarComponent() {
  const [isScrolled, setIsScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const { data: session, status } = useSession();
  const isAuthenticated = status === 'authenticated' && session?.user;
  const isAdmin = status === 'authenticated' && session?.user?.role==='admin';

   const currentPathSegment = pathname?.split('/')[1];
   const isSpecialPath = ['dashboard', 'projects', 'profile', 'admin'].includes(currentPathSegment);

  //  console.log(pathname?.split('/')[1], isSpecialPath)

  // Base navigation links
  const baseNavLinks = [
    { name: 'HOME', href: '/' },
    { name: 'PORTFOLIO', href: '#portfolio' },
    { name: 'SERVICES', href: '#services' },
    { name: 'CONTACT', href: '#contact' },
  ];

  // Profile link - only shown when user is logged in
  const profileLink = { name: 'PROFILE', href: `${settings.url}/profile` };

  // Conditional links based on user role
  // const clientLink = { name: 'CLIENT', href: `${settings.url}/client` };
  // const dashboardLink = { name: 'DASHBOARD', href: `${settings.url}/dashboard` };

  // Combine links based on user role
  const navLinks = [...baseNavLinks];

  // Add PROFILE link if user is authenticated
  if (isAuthenticated) {
    navLinks.splice(3, 0, profileLink);

    // Add CLIENT link if user role is client
    // if (session.user.role === 'client') {
    //   navLinks.splice(4, 0, clientLink);
    // }

    // Add DASHBOARD link if user is admin
    // if (session.user.role === 'admin') {
    //   navLinks.splice(4, 0, dashboardLink);
    // }
  }

  // Handle scroll event to change navbar style
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  return (
    <motion.nav
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.8, delay: 0.2 }}
      className={`flex z-50 fixed top-0 left-0 w-full items-center justify-between px-6 md:px-12 transition-all duration-500 ${
        (isScrolled || pathname?.split('/').length - 1 > 1)
          ? 'bg-white text-neutral-900 h-16 shadow-sm'
          : 'bg-transparent text-white h-24'
      }`}
    >
      {/* Logo */}
      <Link href='/' className={`${isSpecialPath ? 'text-gray-600' : 'text-inherit'} tracking-widest text-xl font-extralight`}>
        luyari.
      </Link>

      {/* Desktop Navigation */}
      {pathname?.split('/').length-1<2 && <div className={`hidden md:flex items-center space-x-10`}>
        {navLinks.map((link, index) => (
          <Link
            key={index}
            href={link.href}
            className={`${isSpecialPath ? 'text-gray-600' : 'text-inherit'} text-xs tracking-widest font-extralight hover:opacity-70 transition-opacity`}
          >
            {link.name}
          </Link>
        ))}
         {session?.user?.role === 'admin' && <div className={`hidden md:flex items-center space-x-10`}>
        <Link
          href={`/admin`}
          className={`${isSpecialPath ? 'text-gray-600' : 'text-inherit'} text-xs uppercase tracking-widest font-extralight hover:opacity-70 transition-opacity`}
        >
          admin
        </Link>
      </div>}
      </div>}
      
      {/* Desktop Navigation */}
      <div className={`${isSpecialPath ? 'text-gray-600' : 'text-inherit'} hidden md:flex items-center space-x-5`}>
       
        <SearchComponent/>
        {/* <MenuComponent/> */}
        <UserSettings/>
      </div>

      {/* Mobile Menu Button */}
      <button
        className="md:hidden text-xl"
        onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
        aria-label="Toggle menu"
      >
        {mobileMenuOpen ? <HiX /> : <HiOutlineMenuAlt3 />}
      </button>

      {/* Mobile Menu */}
      {mobileMenuOpen && (
        <div className="absolute top-full left-0 w-full bg-white text-neutral-900 shadow-sm py-6 md:hidden">
          <div className="flex flex-col space-y-6 px-6">
            {navLinks.map((link, index) => (
              <Link
                key={index}
                href={link.href}
                className="text-xs tracking-widest font-extralight py-2"
                onClick={() => setMobileMenuOpen(false)}
              >
                {link.name}
              </Link>
            ))}
          </div>
        </div>
      )}
    </motion.nav>
  );
}
