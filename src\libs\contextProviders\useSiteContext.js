'use client'
import { INITIAL_SITE_STATE, reducerSiteContext } from './reducerSite'
import React, { createContext, useContext, useReducer, useState } from 'react'

export const SiteContext=createContext()

export default function SiteContextProvider({children}) {
  const [search,setSearch]=useState()
  const [siteState,siteDispatch]=useReducer(reducerSiteContext,INITIAL_SITE_STATE)
  return (
    <SiteContext.Provider
        value={{
          siteState,siteDispatch,
          search,setSearch
        }}
    >
      {children}
    </SiteContext.Provider>
  )
}

export const  useSiteContext=()=>{
    return useContext(SiteContext)
}