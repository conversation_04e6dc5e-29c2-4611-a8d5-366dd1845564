'use client'
import { createContext, useContext, useReducer, useState } from "react"
import { INITIAL_EXPERIENCE_STATE, reducerExperience } from "./reducerExperience.jsx"

export const ExperienceContext=createContext()

export default function ExperienceContextProvider({children}) {
    const [userInfoDetails,setUserInfoDetails]=useState({})
    const [fileArray,setFileArray]=useState([])
    const [dataExperience,setDataExperience]=useState({})
    const [experienceState,experienceDispatch]=useReducer(reducerExperience,INITIAL_EXPERIENCE_STATE)
    return (
        <ExperienceContext.Provider
            value={{dataExperience,setDataExperience,userInfoDetails,setUserInfoDetails,fileArray,setFileArray,experienceState,experienceDispatch}}
        >
            {children}
        </ExperienceContext.Provider>
    )
}
export const useExperienceContext=()=>{
    const context = useContext(ExperienceContext)
    if (!context) {
        throw new Error('useExperienceContext must be used within an ExperienceContextProvider')
    }
    return context
}